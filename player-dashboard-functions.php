<?php
/**
 * Player Dashboard Modülü için <PERSON>on<PERSON>
 */

// Exit if accessed directly
if ( !defined( 'ABSPATH' ) ) exit;

// --- ACF ALAN ADLARI (WP_Query için) ---
define('ACF_FIELD_NAME_ATHLETE_TEAM', 'athlete_team');
define('ACF_FIELD_NAME_SESSION_DATE', 'session_date');
define('ACF_FIELD_NAME_SESSION_START_TIME', 'session_start_time');
define('ACF_FIELD_NAME_SESSION_ASSIGNED_PROGRAM', 'session_assigned_workout_program');
define('ACF_FIELD_NAME_SESSION_PARTICIPANTS', 'session_participants');
define('ACF_FIELD_NAME_SESSION_TEAM_OR_INDIVIDUAL', 'session_team_or_individual');
define('ACF_FIELD_NAME_SESSION_RPE_REPEATER_NAME', 'session_actual_duration_rpe'); // Repeater'ın KENDİ ADI

// Corresponds to the 'session_actual_duration_rpe' repeater
if (!defined('ACF_SESSION_SUB_FIELD_NAME_RPE_PARTICIPANT')) define('ACF_SESSION_SUB_FIELD_NAME_RPE_PARTICIPANT', 'participant_athlete');
if (!defined('ACF_SESSION_SUB_FIELD_NAME_RPE_ACTUAL_DURATION')) define('ACF_SESSION_SUB_FIELD_NAME_RPE_ACTUAL_DURATION', 'actual_duration_minutes');
if (!defined('ACF_SESSION_SUB_FIELD_NAME_RPE_VALUE')) define('ACF_SESSION_SUB_FIELD_NAME_RPE_VALUE', 'rpe');

// --- ACF ALAN ANAHTARLARI (get_field, have_rows vb. için) ---
// Athlete CPT
if (!defined('ACF_ATHLETE_FIELD_KEY_FIRST_NAME')) define('ACF_ATHLETE_FIELD_KEY_FIRST_NAME', 'field_682e80a9f0546');
if (!defined('ACF_ATHLETE_FIELD_KEY_LAST_NAME')) define('ACF_ATHLETE_FIELD_KEY_LAST_NAME', 'field_682e80d4f0547');
if (!defined('ACF_ATHLETE_FIELD_KEY_DOB')) define('ACF_ATHLETE_FIELD_KEY_DOB', 'field_682e8118f0548');
if (!defined('ACF_ATHLETE_FIELD_KEY_JERSEY_NUMBER')) define('ACF_ATHLETE_FIELD_KEY_JERSEY_NUMBER', 'field_68303c650dee1');
if (!defined('ACF_ATHLETE_FIELD_KEY_TEAM')) define('ACF_ATHLETE_FIELD_KEY_TEAM', 'field_682e8143f0549');
if (!defined('ACF_ATHLETE_FIELD_KEY_POSITION')) define('ACF_ATHLETE_FIELD_KEY_POSITION', 'field_682e8164f054a');
if (!defined('ACF_ATHLETE_FIELD_KEY_PHONE')) define('ACF_ATHLETE_FIELD_KEY_PHONE', 'field_682e8188f054b');
if (!defined('ACF_ATHLETE_FIELD_KEY_EMAIL')) define('ACF_ATHLETE_FIELD_KEY_EMAIL', 'field_682e81bbf054c');
if (!defined('ACF_ATHLETE_FIELD_KEY_PROFILE_PICTURE')) define('ACF_ATHLETE_FIELD_KEY_PROFILE_PICTURE', 'field_682e81f8f054d');

// Athlete CPT - GSW Alan Anahtarları
if (!defined('ACF_ATHLETE_FIELD_KEY_GOALS')) define('ACF_ATHLETE_FIELD_KEY_GOALS', 'field_683a08ecb843b');
if (!defined('ACF_ATHLETE_FIELD_KEY_STRENGTHS')) define('ACF_ATHLETE_FIELD_KEY_STRENGTHS', 'field_683a08f4b843c');
if (!defined('ACF_ATHLETE_FIELD_KEY_WEAKNESSES')) define('ACF_ATHLETE_FIELD_KEY_WEAKNESSES', 'field_683a08ffb843d');

// Athlete Notes Repeater Alan Anahtarları
if (!defined('ACF_ATHLETE_NOTES_REPEATER_KEY')) define('ACF_ATHLETE_NOTES_REPEATER_KEY', 'field_6839fac66e7d1'); // Tekrarlayıcının anahtarı (have_rows için)
// Alt alanlar için 'get_sub_field' içinde doğrudan alan adları (örn: 'note_date') kullanılacak.
// Orjinal SUB_KEY tanımları yorumlandı, çünkü get_sub_field içinde alan adları kullanılacak:
// if (!defined('ACF_ATHLETE_NOTE_DATE_SUB_KEY')) define('ACF_ATHLETE_NOTE_DATE_SUB_KEY', 'field_6839fae26e7d2');
// if (!defined('ACF_ATHLETE_NOTE_CONTENT_SUB_KEY')) define('ACF_ATHLETE_NOTE_CONTENT_SUB_KEY', 'field_6839fafd6e7d3');
// if (!defined('ACF_ATHLETE_NOTE_SESSION_LINK_SUB_KEY')) define('ACF_ATHLETE_NOTE_SESSION_LINK_SUB_KEY', 'field_6839fb116e7d4');
// if (!defined('ACF_ATHLETE_NOTE_CONTEXT_SUB_KEY')) define('ACF_ATHLETE_NOTE_CONTEXT_SUB_KEY', 'field_6839fb516e7d5');

// Team CPT
if (!defined('ACF_TEAM_FIELD_KEY_LOGO')) define('ACF_TEAM_FIELD_KEY_LOGO', 'field_682e7fa2b0fcf');
if (!defined('ACF_TEAM_FIELD_KEY_POSITIONS_REPEATER')) define('ACF_TEAM_FIELD_KEY_POSITIONS_REPEATER', 'field_682e7ff0b0fd1'); // Tekrarlayıcının anahtarı (have_rows için)
// Alt alanlar için 'get_sub_field' içinde doğrudan alan adı (örn: 'position_name') kullanılacak.
// if (!defined('ACF_TEAM_SUB_FIELD_KEY_POSITION_NAME')) define('ACF_TEAM_SUB_FIELD_KEY_POSITION_NAME', 'field_682e800bb0fd2');

// Training Session CPT
if (!defined('ACF_SESSION_FIELD_KEY_DATE')) define('ACF_SESSION_FIELD_KEY_DATE', 'field_68306f227337f');
if (!defined('ACF_SESSION_FIELD_KEY_START_TIME')) define('ACF_SESSION_FIELD_KEY_START_TIME', 'field_68306f6373380');
if (!defined('ACF_SESSION_FIELD_KEY_TEAM_OR_INDIVIDUAL')) define('ACF_SESSION_FIELD_KEY_TEAM_OR_INDIVIDUAL', 'field_6830700273382');
if (!defined('ACF_SESSION_FIELD_KEY_PARTICIPANTS')) define('ACF_SESSION_FIELD_KEY_PARTICIPANTS', 'field_6830707673383');
if (!defined('ACF_SESSION_FIELD_KEY_ASSIGNED_PROGRAM')) define('ACF_SESSION_FIELD_KEY_ASSIGNED_PROGRAM', 'field_683072517338b');
// RPE Repeater
if (!defined('ACF_SESSION_RPE_REPEATER_KEY')) define('ACF_SESSION_RPE_REPEATER_KEY', 'field_683072ad7338c'); // Tekrarlayıcının anahtarı (get_field veya have_rows için)
// ACF_SESSION_SUB_FIELD_NAME_* sabitleri zaten yukarıda alan adı olarak tanımlı ve bunlar kullanılacak.

// Workout CPT Alanları (Kuvvet Programı)
if (!defined('ACF_WORKOUT_FIELD_EXERCISE_LIST')) define('ACF_WORKOUT_FIELD_EXERCISE_LIST', 'field_681da67f16d64'); // Tekrarlayıcı anahtarı (have_rows için)
// Alt alanlar için 'get_sub_field' içinde doğrudan alan adları (örn: 'exercise_name') kullanılacak.

// --- PLAYER DASHBOARD FONKSİYONLARI ---

if ( ! function_exists( 'coachai_pd_get_team_athletes_list' ) ) {
    function coachai_pd_get_team_athletes_list( $team_id, $current_lang = 'en' ) { // Varsayılan dil 'en' olarak ayarlandı
        $athletes_data = array();

        $team_logo_url = 'https://ui-avatars.com/api/?name=T&size=40&background=0284c7&color=fff&bold=true';
        if ($team_id) {
            $team_logo_data = get_field( ACF_TEAM_FIELD_KEY_LOGO, $team_id );
            if ( $team_logo_data && is_array( $team_logo_data ) && isset( $team_logo_data['sizes']['thumbnail'] ) ) {
                $team_logo_url = $team_logo_data['sizes']['thumbnail'];
            } elseif ( $team_logo_data && is_array( $team_logo_data ) && isset( $team_logo_data['url'] ) ) {
                $team_logo_url = $team_logo_data['url'];
            }
        }
        $athletes_data[] = array(
            'id'         => 'team_average',
            'name'       => ($current_lang === 'tr' ? 'Takım Ortalaması' : 'Team Average'),
            'first_name' => ($current_lang === 'tr' ? 'Takım' : 'Team'),
            'last_name'  => ($current_lang === 'tr' ? 'Ortalaması' : 'Average'),
            'avatar_url' => $team_logo_url,
        );

        if ( empty( $team_id ) ) {
            return $athletes_data;
        }

        $athletes_args = array(
            'post_type'      => 'athlete',
            'posts_per_page' => -1,
            'meta_query'     => array(
                array(
                    'key'     => ACF_FIELD_NAME_ATHLETE_TEAM,
                    'value'   => $team_id,
                    'compare' => '=',
                ),
            ),
            'orderby'        => 'title',
            'order'          => 'ASC',
            'post_status'    => 'publish',
            'lang'           => $current_lang, // WPML veya Polylang için dil parametresi
        );

        $athletes_query = new WP_Query( $athletes_args );

        if ( $athletes_query->have_posts() ) {
            while ( $athletes_query->have_posts() ) {
                $athletes_query->the_post();
                $athlete_id         = get_the_ID();
                $athlete_first_name = get_field( ACF_ATHLETE_FIELD_KEY_FIRST_NAME, $athlete_id );
                $athlete_last_name  = get_field( ACF_ATHLETE_FIELD_KEY_LAST_NAME, $athlete_id );

                $display_name = trim( $athlete_first_name . ' ' . $athlete_last_name );
                if ( empty( $display_name ) ) {
                    $display_name = get_the_title($athlete_id);
                }
                if ( empty( $display_name ) || $display_name === 'Otomatik Taslak' || $display_name === 'Auto Draft') {
                    $display_name = ($current_lang === 'tr' ? 'İsimsiz Sporcu' : 'Unnamed Athlete') . ' #' . $athlete_id;
                }

                $profile_pic_data = get_field( ACF_ATHLETE_FIELD_KEY_PROFILE_PICTURE, $athlete_id );
                $profile_pic_url  = 'https://ui-avatars.com/api/?name=' . urlencode( $display_name ) . '&size=40&background=e0e7ff&color=4338ca&font-size=0.4&bold=true';
                if ( $profile_pic_data && is_array( $profile_pic_data ) && isset( $profile_pic_data['sizes']['thumbnail'] ) ) {
                    $profile_pic_url = $profile_pic_data['sizes']['thumbnail'];
                } elseif ( $profile_pic_data && is_array( $profile_pic_data ) && isset( $profile_pic_data['url'] ) ) {
                    $profile_pic_url = $profile_pic_data['url'];
                } elseif ( is_numeric( $profile_pic_data ) ) {
                    $profile_pic_url_temp = wp_get_attachment_image_url( $profile_pic_data, 'thumbnail' );
                    if ( $profile_pic_url_temp ) {
                        $profile_pic_url = $profile_pic_url_temp;
                    }
                }

                $athletes_data[] = array(
                    'id'         => $athlete_id,
                    'name'       => $display_name,
                    'first_name' => $athlete_first_name,
                    'last_name'  => $athlete_last_name,
                    'avatar_url' => $profile_pic_url,
                );
            }
            wp_reset_postdata();
        }
        return $athletes_data;
    }
}

if ( ! function_exists( 'coachai_pd_get_athlete_gsw_data' ) ) {
    function coachai_pd_get_athlete_gsw_data( $athlete_id ) {
        if ( ! $athlete_id || get_post_type( $athlete_id ) !== 'athlete' ) {
            return null;
        }
        $goals_raw = get_field( ACF_ATHLETE_FIELD_KEY_GOALS, $athlete_id );
        $strengths_raw = get_field( ACF_ATHLETE_FIELD_KEY_STRENGTHS, $athlete_id );
        $weaknesses_raw = get_field( ACF_ATHLETE_FIELD_KEY_WEAKNESSES, $athlete_id );

        $format_text_to_html_list_or_wpautop = function($text_content) {
            if (empty(trim($text_content))) return '';
            if (strpos(trim($text_content), '<ul>') === 0) return $text_content;
            $items = preg_split('/\r\n|\r|\n/', trim($text_content));
            $filtered_items = array_filter($items, function($item) {
                return !empty(trim($item));
            });
            if (count($filtered_items) > 1) {
                $html = '<ul>';
                foreach($filtered_items as $item) {
                    $html .= '<li>' . esc_html(trim($item)) . '</li>';
                }
                $html .= '</ul>';
                return $html;
            } elseif (count($filtered_items) === 1) {
                return '<p>' . esc_html(trim($filtered_items[0])) . '</p>';
            }
            return '';
        };

        return array(
            'goals'      => $format_text_to_html_list_or_wpautop($goals_raw),
            'strengths'  => $format_text_to_html_list_or_wpautop($strengths_raw),
            'weaknesses' => $format_text_to_html_list_or_wpautop($weaknesses_raw),
            'raw' => [
                'goals' => $goals_raw ?: '',
                'strengths' => $strengths_raw ?: '',
                'weaknesses' => $weaknesses_raw ?: '',
            ]
        );
    }
}

if ( ! function_exists( 'coachai_pd_get_last_assigned_workouts' ) ) {
    function coachai_pd_get_last_assigned_workouts( $team_id, $current_user_id, $current_lang = 'en', $count = 5, $player_id = null ) {
        $workouts_data = array();
        $i18n_temp = [
            'tr' => ['players_suffix' => 'Oyuncu', 'team_label' => 'TAKIM', 'individual_label' => 'BİREYSEL'],
            'en' => ['players_suffix' => 'Players', 'team_label' => 'TEAM', 'individual_label' => 'INDIVIDUAL']
        ];
        $td_temp = $i18n_temp[$current_lang] ?? $i18n_temp['en'];

        $args = array(
            'post_type'      => 'training_session',
            'posts_per_page' => $count,
            'author'         => $current_user_id,
            'orderby'        => array(
                'session_date_clause'       => 'DESC',
                'session_start_time_clause' => 'DESC',
            ),
            'post_status'    => 'publish',
            'lang'           => $current_lang,
            'meta_query'     => array(
                'relation' => 'AND',
                'session_date_clause' => array(
                    'key'  => ACF_FIELD_NAME_SESSION_DATE,
                    'type' => 'DATE',
                ),
                'session_start_time_clause' => array(
                    'key'     => ACF_FIELD_NAME_SESSION_START_TIME,
                    'compare' => 'EXISTS',
                ),
                array(
                    'key'     => ACF_FIELD_NAME_SESSION_ASSIGNED_PROGRAM,
                    'compare' => 'EXISTS',
                ),
                array(
                    'key'     => ACF_FIELD_NAME_SESSION_ASSIGNED_PROGRAM,
                    'value'   => '',
                    'compare' => '!=',
                ),
            ),
        );

        if ( $player_id !== null && $player_id > 0 && $player_id !== 'team_average' ) {
            $args['meta_query'][] = array(
                'key'     => ACF_FIELD_NAME_SESSION_PARTICIPANTS,
                'value'   => '"' . $player_id . '"',
                'compare' => 'LIKE',
            );
        }

        $sessions_query = new WP_Query( $args );
        if ( $sessions_query->have_posts() ) {
            while ( $sessions_query->have_posts() ) {
                $sessions_query->the_post();
                $session_id = get_the_ID();

                $assigned_program_obj = get_field( ACF_SESSION_FIELD_KEY_ASSIGNED_PROGRAM, $session_id );
                if ( ! $assigned_program_obj || ! ( $assigned_program_obj instanceof WP_Post ) ) {
                    continue;
                }

                $assigned_program_id    = $assigned_program_obj->ID;
                $assigned_program_title = get_the_title( $assigned_program_id );
                $session_type_raw       = get_field( ACF_SESSION_FIELD_KEY_TEAM_OR_INDIVIDUAL, $session_id );
                $session_type_value     = is_array($session_type_raw) ? ($session_type_raw['value'] ?? 'individual') : (is_string($session_type_raw) ? $session_type_raw : 'individual');
                $session_type           = strtolower($session_type_value);

                $participants_raw     = get_field( ACF_SESSION_FIELD_KEY_PARTICIPANTS, $session_id );
                $participants_display = '';
                $participant_first_names = array();

                if ( ! empty( $participants_raw ) ) {
                    $participants_array = is_array( $participants_raw ) ? $participants_raw : array( $participants_raw );
                    foreach ( $participants_array as $p_obj_or_id ) {
                        $p_id_loop = 0;
                        if ( $p_obj_or_id instanceof WP_Post ) $p_id_loop = $p_obj_or_id->ID;
                        elseif ( is_numeric( $p_obj_or_id ) ) $p_id_loop = intval( $p_obj_or_id );
                        elseif (is_array($p_obj_or_id) && isset($p_obj_or_id['ID'])) $p_id_loop = intval($p_obj_or_id['ID']);

                        if ( $p_id_loop > 0 ) {
                            $first_name = get_field( ACF_ATHLETE_FIELD_KEY_FIRST_NAME, $p_id_loop );
                            if (empty($first_name)) $first_name = strtok(get_the_title($p_id_loop), " ");
                            if (!empty($first_name)) $participant_first_names[] = $first_name;
                        }
                    }
                }

                $num_participants = count( $participant_first_names );
                if ( $num_participants === 1 ) {
                    $participants_display = $participant_first_names[0];
                } elseif ( $num_participants === 2 ) {
                    $participants_display = implode( ( $current_lang === 'tr' ? ' ve ' : ' and ' ), $participant_first_names );
                } elseif ( $num_participants > 2 ) {
                    $participants_display = $num_participants . ' ' . ( $current_lang === 'tr' ? ($td_temp['players_suffix'] ?? 'Oyuncu') : ($td_temp['players_suffix'] ?? 'Players') );
                } elseif ($num_participants === 0 && ($session_type === 'individual' || $session_type === 'bireysel')) { // bireysel is Turkish value
                    $participants_display = ($current_lang === 'tr' ? 'Bireysel Atama' : 'Individual Assignment');
                } else {
                    $participants_display = ($current_lang === 'tr' ? 'Katılımcı Yok' : 'No Participants');
                }

                $workouts_data[] = array(
                    'session_id'             => $session_id,
                    'session_title'          => get_the_title( $session_id ),
                    'session_date'           => get_field( ACF_SESSION_FIELD_KEY_DATE, $session_id ),
                    'session_start_time'     => get_field( ACF_SESSION_FIELD_KEY_START_TIME, $session_id ),
                    'assigned_program_id'    => $assigned_program_id,
                    'assigned_program_title' => $assigned_program_title,
                    'session_type'           => $session_type,
                    'participants_display'   => $participants_display,
                );
            }
            wp_reset_postdata();
        }
        return $workouts_data;
    }
}

// GÜNCELLENDİ: coachai_pd_get_recent_notes
if ( ! function_exists( 'coachai_pd_get_recent_notes' ) ) {
    function coachai_pd_get_recent_notes( $team_id, $current_user_id, $current_lang = 'en', $count = 10, $player_id_or_team_avg = null ) {
        $recent_notes = array();
        $all_notes_temp = array();

        $athletes_args = array(
            'post_type'      => 'athlete',
            'posts_per_page' => -1,
            'orderby'        => 'title',
            'order'          => 'ASC',
            'post_status'    => 'publish',
            'lang'           => $current_lang,
        );

        if ( $player_id_or_team_avg !== null && $player_id_or_team_avg !== 'team_average' && $player_id_or_team_avg > 0 ) {
            $athletes_args['post__in'] = array( $player_id_or_team_avg );
        } elseif ( $team_id !== null && $team_id > 0 ) {
            $athletes_args['meta_query'] = array(
                array(
                    'key'     => ACF_FIELD_NAME_ATHLETE_TEAM,
                    'value'   => $team_id,
                    'compare' => '=',
                ),
            );
        } else {
            return $recent_notes;
        }

        $athletes_query = new WP_Query( $athletes_args );

        if ( $athletes_query->have_posts() ) {
            while ( $athletes_query->have_posts() ) {
                $athletes_query->the_post();
                $current_athlete_id = get_the_ID();
                $athlete_first_name = get_field( ACF_ATHLETE_FIELD_KEY_FIRST_NAME, $current_athlete_id );
                $athlete_last_name  = get_field( ACF_ATHLETE_FIELD_KEY_LAST_NAME, $current_athlete_id );
                $athlete_display_name = trim( $athlete_first_name . ' ' . $athlete_last_name );
                if(empty($athlete_display_name)) $athlete_display_name = get_the_title($current_athlete_id);

                if ( have_rows( ACF_ATHLETE_NOTES_REPEATER_KEY, $current_athlete_id ) ) {
                    while ( have_rows( ACF_ATHLETE_NOTES_REPEATER_KEY, $current_athlete_id ) ) {
                        the_row();
                        // Kullanılacak tahmini alan adları: 'note_date', 'note_content', 'note_session_link', 'note_context'
                        // Lütfen bu adların ACF kurulumunuzdaki gerçek adlar olduğundan emin olun.
                        $note_date_raw    = get_sub_field( 'note_date' );
                        $note_content     = get_sub_field( 'note_content' );
                        $note_session_obj = get_sub_field( 'note_session_link' );
                        $note_context_raw = get_sub_field( 'note_context' );
                        $note_context     = is_array($note_context_raw) ? ($note_context_raw['label'] ?? ($note_context_raw['value'] ?? '')) : $note_context_raw;

                        if ( $note_date_raw && $note_content ) {
                            $related_session_title = null;
                            $related_session_date_formatted = null;
                            if ($note_session_obj instanceof WP_Post) {
                                $related_session_title = get_the_title($note_session_obj->ID);
                                $session_date_for_note = get_field(ACF_SESSION_FIELD_KEY_DATE, $note_session_obj->ID);
                                if ($session_date_for_note) {
                                    $related_session_date_formatted = date_i18n(get_option('date_format'), strtotime($session_date_for_note));
                                }
                            }

                            $note_date_display_formatted = date_i18n( get_option( 'date_format' ), strtotime( $note_date_raw ) );
                            $note_meta_display_for_card = $athlete_display_name . ' - ' . $note_date_display_formatted;
                            if ($related_session_title) {
                                $note_meta_display_for_card .= ($related_session_date_formatted ? ' (' . $related_session_date_formatted . ' - ' : ' (') . esc_html($related_session_title) . ')';
                            }

                            $all_notes_temp[] = array(
                                'note_unique_id'    => 'athlete_' . $current_athlete_id . '_row_' . get_row_index(),
                                'athlete_id'        => $current_athlete_id,
                                'athlete_name'      => $athlete_display_name,
                                'note_date_raw'     => $note_date_raw,
                                'note_date'         => $note_date_display_formatted,
                                'note_meta_display' => $note_meta_display_for_card,
                                'note_excerpt'      => wp_trim_words( strip_tags($note_content), 15, '...' ),
                                'full_note_content' => $note_content,
                                'related_session_id'=> ($note_session_obj instanceof WP_Post) ? $note_session_obj->ID : null,
                                'related_session_title' => $related_session_title,
                                'note_context'      => $note_context,
                            );
                        }
                    }
                }
            }
            wp_reset_postdata();
        }

        if ( ! empty( $all_notes_temp ) ) {
            usort( $all_notes_temp, function( $a, $b ) {
                return strtotime( $b['note_date_raw'] ) - strtotime( $a['note_date_raw'] );
            } );
            $recent_notes = array_slice( $all_notes_temp, 0, $count );
        }
        return $recent_notes;
    }
}

// coachai_pd_get_weekly_team_training_load_stats (KULLANICI TARAFINDAN DOĞRU OLDUĞU BELİRTİLEN HALİ)
if ( ! function_exists( 'coachai_pd_get_weekly_team_training_load_stats' ) ) {
    function coachai_pd_get_weekly_team_training_load_stats( $team_id, $current_user_id, $current_lang = 'en', $weeks_count = 8, $player_id_or_team_avg = null ) {
        $chart_data = array(
            'labels'                    => array(),
            'weekly_total_loads'        => array(),
            'load_changes_percentage'   => array(),
        );

        $wp_timezone = wp_timezone();
        $start_of_week_option_wp = (int) get_option( 'start_of_week', 1 );
        $daily_training_loads = array();
        $today_for_calc = new DateTime("now", $wp_timezone);
        $days_to_subtract_for_current_week_monday = ($today_for_calc->format('w') - $start_of_week_option_wp + 7) % 7;
        $current_week_monday = (clone $today_for_calc)->modify("-{$days_to_subtract_for_current_week_monday} days");
        $start_date_of_interest = (clone $current_week_monday)->modify('-' . ($weeks_count -1) . ' weeks')->setTime(0,0,0);
        $end_date_of_interest = (clone $current_week_monday)->modify('+6 days')->setTime(23,59,59);

        $sessions_args = array(
            'post_type'      => 'training_session',
            'posts_per_page' => -1,
            'author'         => $current_user_id,
            'post_status'    => 'publish',
            'lang'           => $current_lang,
            'meta_query'     => array(
                'relation' => 'AND',
                array(
                    'key'     => ACF_FIELD_NAME_SESSION_DATE,
                    'value'   => array( $start_date_of_interest->format('Ymd'), $end_date_of_interest->format('Ymd') ),
                    'type'    => 'DATE',
                    'compare' => 'BETWEEN',
                ),
                array(
                    'key'     => ACF_FIELD_NAME_SESSION_RPE_REPEATER_NAME,
                    'compare' => 'EXISTS'
                ),
                array(
                    'key'     => ACF_FIELD_NAME_SESSION_RPE_REPEATER_NAME,
                    'value'   => '',
                    'compare' => '!='
                )
            ),
            'orderby' => array(ACF_FIELD_NAME_SESSION_DATE => 'ASC'),
        );

        if ($player_id_or_team_avg === 'team_average' || $player_id_or_team_avg === null) {
            $sessions_args['meta_query'][] = array(
                'key'     => ACF_FIELD_NAME_SESSION_TEAM_OR_INDIVIDUAL,
                'value'   => 'team',
                'compare' => '=',
            );
        }

        $sessions_query = new WP_Query( $sessions_args );

        if ($sessions_query->have_posts()) {
            while ($sessions_query->have_posts()) {
                $sessions_query->the_post();
                $session_id_loop = get_the_ID();
                $session_date_str = get_field(ACF_SESSION_FIELD_KEY_DATE, $session_id_loop);

                if (!$session_date_str) {
                    continue;
                }
                try {
                    $session_date_obj_for_key = DateTime::createFromFormat('Ymd', $session_date_str, $wp_timezone);
                    if (!$session_date_obj_for_key) { // Try another common format if Ymd fails (d/m/Y)
                        $session_date_obj_for_key = DateTime::createFromFormat('d/m/Y', $session_date_str, $wp_timezone);
                    }
                } catch (Exception $e) {
                     error_log("Date parsing error for session {$session_id_loop}: " . $e->getMessage());
                    continue;
                }

                if (!$session_date_obj_for_key) {
                    error_log("Could not parse date string '{$session_date_str}' for Session ID: {$session_id_loop}");
                    continue;
                }
                $formatted_date_key = $session_date_obj_for_key->format('Y-m-d');

                if (!isset($daily_training_loads[$formatted_date_key])) {
                    $daily_training_loads[$formatted_date_key] = 0;
                }

                $rpe_repeater_data = get_field(ACF_SESSION_RPE_REPEATER_KEY, $session_id_loop);

                if (is_array($rpe_repeater_data) && !empty($rpe_repeater_data)) {
                    $session_total_load_for_team_avg = 0;
                    $participants_in_session_for_team_avg = 0;

                    foreach ($rpe_repeater_data as $rpe_entry) {
                        $rpe_val_raw = $rpe_entry[ACF_SESSION_SUB_FIELD_NAME_RPE_VALUE] ?? null;
                        $duration_val_raw = $rpe_entry[ACF_SESSION_SUB_FIELD_NAME_RPE_ACTUAL_DURATION] ?? null;
                        $participant_obj_or_id_in_rpe = $rpe_entry[ACF_SESSION_SUB_FIELD_NAME_RPE_PARTICIPANT] ?? null;

                        $rpe_val = isset($rpe_val_raw) && is_numeric($rpe_val_raw) ? (float)$rpe_val_raw : 0;
                        $duration_val = isset($duration_val_raw) && is_numeric($duration_val_raw) ? (float)$duration_val_raw : 0;

                        if ($rpe_val > 0 && $duration_val > 0) {
                            $training_load_entry = $rpe_val * $duration_val;

                            if ($player_id_or_team_avg && $player_id_or_team_avg !== 'team_average') {
                                $p_id_check = 0;
                                if ($participant_obj_or_id_in_rpe instanceof WP_Post) $p_id_check = $participant_obj_or_id_in_rpe->ID;
                                elseif (is_numeric($participant_obj_or_id_in_rpe)) $p_id_check = intval($participant_obj_or_id_in_rpe);
                                elseif (is_array($participant_obj_or_id_in_rpe) && isset($participant_obj_or_id_in_rpe['ID'])) $p_id_check = intval($participant_obj_or_id_in_rpe['ID']);

                                if ($p_id_check == $player_id_or_team_avg) {
                                    $daily_training_loads[$formatted_date_key] += $training_load_entry;
                                }
                            } else {
                                $session_total_load_for_team_avg += $training_load_entry;
                                $participants_in_session_for_team_avg++;
                            }
                        }
                    }

                    if (($player_id_or_team_avg === 'team_average' || $player_id_or_team_avg === null) && $participants_in_session_for_team_avg > 0) {
                        $average_load_for_session = $session_total_load_for_team_avg / $participants_in_session_for_team_avg;
                        $daily_training_loads[$formatted_date_key] += $average_load_for_session;
                    }
                }
            }
            wp_reset_postdata();
        }

        $collected_weekly_loads_for_chart = [];
        for ($w = ($weeks_count - 1); $w >= 0; $w--) {
            $week_monday = (clone $current_week_monday)->modify('-' . $w . ' weeks');
            $week_sunday = (clone $week_monday)->modify('+6 days');
            $week_label_display = $week_monday->format('M j') . ($current_lang === 'tr' ? ' - ' : ' - ') . $week_sunday->format('M j, Y');
            $chart_data['labels'][] = $week_label_display;
            $current_week_total_load_sum = 0;
            $loop_date_for_sum = clone $week_monday;
            while ($loop_date_for_sum <= $week_sunday) {
                $loop_date_key_for_sum = $loop_date_for_sum->format('Y-m-d');
                if (isset($daily_training_loads[$loop_date_key_for_sum])) {
                    $current_week_total_load_sum += $daily_training_loads[$loop_date_key_for_sum];
                }
                $loop_date_for_sum->modify('+1 day');
            }
            $collected_weekly_loads_for_chart[] = round($current_week_total_load_sum, 0);
        }

        $chart_data['weekly_total_loads'] = $collected_weekly_loads_for_chart;
        $previous_week_load_for_change = null;
        foreach ($collected_weekly_loads_for_chart as $current_load_for_change) {
            $change_percentage_value = 0;
            if ($previous_week_load_for_change !== null) {
                if ($previous_week_load_for_change > 0) {
                    $change_percentage_value = round((($current_load_for_change - $previous_week_load_for_change) / $previous_week_load_for_change) * 100, 1);
                } elseif ($current_load_for_change > 0) {
                    $change_percentage_value = 100.0;
                }
            }
            $chart_data['load_changes_percentage'][] = $change_percentage_value;
            $previous_week_load_for_change = $current_load_for_change;
        }
        return $chart_data;
    }
}


// GÜNCELLENDİ: coachai_pd_get_athlete_details_data
if ( ! function_exists( 'coachai_pd_get_athlete_details_data' ) ) {
    function coachai_pd_get_athlete_details_data( $athlete_id, $current_lang = 'en' ) {
        if ( ! $athlete_id || get_post_type( $athlete_id ) !== 'athlete' ) {
            return null;
        }
        $athlete_post = get_post( $athlete_id );
        if ( ! $athlete_post ) return null;

        $first_name = get_field( ACF_ATHLETE_FIELD_KEY_FIRST_NAME, $athlete_id );
        $last_name  = get_field( ACF_ATHLETE_FIELD_KEY_LAST_NAME, $athlete_id );
        $full_name  = trim( $first_name . ' ' . $last_name );
        if ( empty( $full_name ) ) $full_name = $athlete_post->post_title;

        $profile_pic_data = get_field( ACF_ATHLETE_FIELD_KEY_PROFILE_PICTURE, $athlete_id );
        $profile_pic_url  = 'https://ui-avatars.com/api/?name=' . urlencode( $full_name ) . '&size=100&background=e0e7ff&color=4338ca&font-size=0.4';
        if ( $profile_pic_data && is_array( $profile_pic_data ) && isset( $profile_pic_data['sizes']['medium_large'] ) ) {
            $profile_pic_url = $profile_pic_data['sizes']['medium_large'];
        } elseif ( $profile_pic_data && is_array( $profile_pic_data ) && isset( $profile_pic_data['sizes']['medium'] ) ) {
            $profile_pic_url = $profile_pic_data['sizes']['medium'];
        } elseif ( $profile_pic_data && is_array( $profile_pic_data ) && isset( $profile_pic_data['url'] ) ) {
            $profile_pic_url = $profile_pic_data['url'];
        } elseif ( is_numeric( $profile_pic_data ) ) {
            $profile_pic_url_temp = wp_get_attachment_image_url( $profile_pic_data, 'medium_large' );
            if ( $profile_pic_url_temp ) $profile_pic_url = $profile_pic_url_temp;
        }

        $dob_raw = get_field( ACF_ATHLETE_FIELD_KEY_DOB, $athlete_id );
        $dob_display = $dob_raw ? date_i18n( get_option('date_format'), strtotime( $dob_raw ) ) : '-';

        $position_slug = get_field( ACF_ATHLETE_FIELD_KEY_POSITION, $athlete_id );
        $position_display = '-';

        if ($position_slug) {
            $athlete_team_obj = get_field( ACF_ATHLETE_FIELD_KEY_TEAM, $athlete_id );
            $athlete_team_id = 0;
            if ($athlete_team_obj instanceof WP_Post) {
                $athlete_team_id = $athlete_team_obj->ID;
            } elseif (is_numeric($athlete_team_obj)) {
                $athlete_team_id = intval($athlete_team_obj);
            }

            if ($athlete_team_id && have_rows( ACF_TEAM_FIELD_KEY_POSITIONS_REPEATER, $athlete_team_id )) {
                while(have_rows( ACF_TEAM_FIELD_KEY_POSITIONS_REPEATER, $athlete_team_id )): the_row();
                    // Kullanılacak tahmini alan adı: 'position_name'
                    $repeater_pos_name = get_sub_field( 'position_name' );
                    if (sanitize_title($repeater_pos_name) === $position_slug) {
                        $position_display = $repeater_pos_name;
                        break;
                    }
                endwhile;
            }
            if ($position_display === '-' && $position_slug) {
                $position_display = ucfirst(str_replace(array('-', '_'), ' ', $position_slug));
            }
        }

        $goals = get_field( ACF_ATHLETE_FIELD_KEY_GOALS, $athlete_id ) ?: '';
        $strengths = get_field( ACF_ATHLETE_FIELD_KEY_STRENGTHS, $athlete_id ) ?: '';
        $weaknesses = get_field( ACF_ATHLETE_FIELD_KEY_WEAKNESSES, $athlete_id ) ?: '';

        return array(
            'id'              => $athlete_id,
            'full_name'       => $full_name,
            'first_name'      => $first_name,
            'last_name'       => $last_name,
            'profile_pic_url' => $profile_pic_url,
            'jersey_number'   => get_field( ACF_ATHLETE_FIELD_KEY_JERSEY_NUMBER, $athlete_id ) ?: '-',
            'position'        => $position_display,
            'dob'             => $dob_display,
            'email'           => get_field( ACF_ATHLETE_FIELD_KEY_EMAIL, $athlete_id ) ?: '-',
            'phone'           => get_field( ACF_ATHLETE_FIELD_KEY_PHONE, $athlete_id ) ?: '-',
            'goals'           => $goals,
            'strengths'       => $strengths,
            'weaknesses'      => $weaknesses,
        );
    }
}

if ( ! function_exists( 'coachai_pd_get_daily_training_loads_for_athlete' ) ) {
    function coachai_pd_get_daily_training_loads_for_athlete( $athlete_id, $start_date_ymd, $end_date_ymd, $current_user_id ) {
        $daily_loads = [];
        $wp_timezone = wp_timezone();

        try {
            $start_dt = new DateTime( $start_date_ymd, $wp_timezone );
            $end_dt   = new DateTime( $end_date_ymd, $wp_timezone );
        } catch (Exception $e) {
            error_log('Invalid date format for coachai_pd_get_daily_training_loads_for_athlete: ' . $e->getMessage());
            return $daily_loads;
        }

        $interval = new DateInterval('P1D');
        $daterange = new DatePeriod($start_dt, $interval, (clone $end_dt)->modify('+1 day'));

        foreach($daterange as $date_obj){
            $daily_loads[ $date_obj->format('Ymd') ] = 0;
        }

        $args = array(
            'post_type'      => 'training_session',
            'posts_per_page' => -1,
            'author'         => $current_user_id,
            'post_status'    => 'publish',
            'meta_query'     => array(
                'relation' => 'AND',
                array(
                    'key'     => ACF_SESSION_FIELD_KEY_DATE,
                    'value'   => array( $start_date_ymd, $end_date_ymd ),
                    'type'    => 'DATE',
                    'compare' => 'BETWEEN',
                ),
                array(
                    'key'     => ACF_SESSION_RPE_REPEATER_KEY,
                    'compare' => 'EXISTS',
                ),
                array(
                    'key'     => ACF_SESSION_RPE_REPEATER_KEY,
                    'value'   => '',
                    'compare' => '!=',
                )
            ),
            'orderby' => array(ACF_SESSION_FIELD_KEY_DATE => 'ASC'),
        );

        $sessions_query = new WP_Query( $args );

        if ( $sessions_query->have_posts() ) {
            while ( $sessions_query->have_posts() ) {
                $sessions_query->the_post();
                $session_id_loop = get_the_ID();
                $session_date_raw = get_field( ACF_SESSION_FIELD_KEY_DATE, $session_id_loop );

                try {
                    $session_date_obj = DateTime::createFromFormat('Ymd', $session_date_raw, $wp_timezone);
                     if (!$session_date_obj) {
                        $session_date_obj = DateTime::createFromFormat('d/m/Y', $session_date_raw, $wp_timezone);
                    }
                } catch (Exception $e) {
                     error_log("Date parsing error for session {$session_id_loop} (date: {$session_date_raw}): " . $e->getMessage());
                    continue;
                }


                if (!$session_date_obj) {
                    error_log("coachai_pd_get_daily_training_loads_for_athlete: Could not parse date '{$session_date_raw}' for session {$session_id_loop}.");
                    continue;
                }
                $session_date_ymd_key = $session_date_obj->format('Ymd');

                $rpe_repeater_data = get_field( ACF_SESSION_RPE_REPEATER_KEY, $session_id_loop );

                if ( is_array($rpe_repeater_data) && !empty($rpe_repeater_data) ) {
                    foreach ( $rpe_repeater_data as $rpe_entry ) {
                        $participant_obj_or_id = $rpe_entry[ACF_SESSION_SUB_FIELD_NAME_RPE_PARTICIPANT] ?? null;
                        $rpe_val_raw           = $rpe_entry[ACF_SESSION_SUB_FIELD_NAME_RPE_VALUE] ?? null;
                        $duration_val_raw      = $rpe_entry[ACF_SESSION_SUB_FIELD_NAME_RPE_ACTUAL_DURATION] ?? null;

                        $rpe_val = isset($rpe_val_raw) && is_numeric($rpe_val_raw) ? (float)$rpe_val_raw : 0;
                        $duration_val = isset($duration_val_raw) && is_numeric($duration_val_raw) ? (float)$duration_val_raw : 0;

                        $participant_id_check = 0;
                        if ($participant_obj_or_id instanceof WP_Post) {
                            $participant_id_check = $participant_obj_or_id->ID;
                        } elseif (is_numeric($participant_obj_or_id)) {
                            $participant_id_check = intval($participant_obj_or_id);
                        } elseif (is_array($participant_obj_or_id) && isset($participant_obj_or_id['ID'])) {
                            $participant_id_check = intval($participant_obj_or_id['ID']);
                        }

                        if ( $participant_id_check == $athlete_id && $rpe_val > 0 && $duration_val > 0 ) {
                            if (isset($daily_loads[$session_date_ymd_key])) {
                                $daily_loads[$session_date_ymd_key] += ($rpe_val * $duration_val);
                            } else {
                                // Bu durum normalde olmamalı çünkü daterange ile tüm günler başlatıldı.
                                $daily_loads[$session_date_ymd_key] = ($rpe_val * $duration_val);
                                error_log("coachai_pd_get_daily_training_loads_for_athlete: Date key {$session_date_ymd_key} was not pre-initialized for athlete {$athlete_id}. This might indicate a date range issue.");
                            }
                        }
                    }
                }
            }
            wp_reset_postdata();
        }
        return $daily_loads;
    }
}

if ( ! function_exists( 'coachai_pd_calculate_rolling_average_acwr' ) ) {
    function coachai_pd_calculate_rolling_average_acwr( $daily_loads, $acute_window = 7, $chronic_window = 28 ) {
        $acwr_values = [];
        if (empty($daily_loads)) return $acwr_values; // Eğer yük verisi yoksa boş dön
        $sorted_dates = array_keys( $daily_loads );
        sort( $sorted_dates );

        for ( $i = 0; $i < count( $sorted_dates ); $i++ ) {
            $current_date_ymd = $sorted_dates[$i];
            $acute_sum = 0; $acute_count = 0;
            $chronic_sum = 0; $chronic_count = 0;

            for ( $j = 0; $j < $acute_window; $j++ ) {
                if ( $i - $j >= 0 ) {
                    $date_key = $sorted_dates[ $i - $j ];
                    if (isset($daily_loads[$date_key])) { // Yük olduğundan emin ol
                        $acute_sum += $daily_loads[$date_key];
                    }
                    $acute_count++; // Her zaman say, ortalama için
                }
            }

            for ( $k = 0; $k < $chronic_window; $k++ ) {
                if ( $i - $k >= 0 ) {
                    $date_key = $sorted_dates[ $i - $k ];
                     if (isset($daily_loads[$date_key])) { // Yük olduğundan emin ol
                        $chronic_sum += $daily_loads[$date_key];
                    }
                    $chronic_count++; // Her zaman say, ortalama için
                }
            }

            $avg_acute = ($acute_count > 0) ? $acute_sum / $acute_count : 0;
            $avg_chronic = ($chronic_count > 0) ? $chronic_sum / $chronic_count : 0;

            if ($avg_chronic > 0 && $chronic_count >= $acute_window) { // Kronik yük 0'dan büyük olmalı
                $acwr_values[$current_date_ymd] = round($avg_acute / $avg_chronic, 2);
            } else {
                $acwr_values[$current_date_ymd] = null;
            }
        }
        return $acwr_values;
    }
}

if ( ! function_exists( 'coachai_pd_calculate_ewma_acwr' ) ) {
    function coachai_pd_calculate_ewma_acwr( $daily_loads, $alpha_acute = 0.2857, $alpha_chronic = 0.0689 ) {
        $ewma_acwr_values = [];
        if (empty($daily_loads)) return $ewma_acwr_values;
        $sorted_dates = array_keys( $daily_loads );
        sort( $sorted_dates );

        $ewma_acute_prev = null;
        $ewma_chronic_prev = null;

        foreach ( $sorted_dates as $date_ymd ) {
            $current_load = isset($daily_loads[$date_ymd]) ? $daily_loads[$date_ymd] : 0; // Yük yoksa 0 al

            if ( $ewma_acute_prev === null ) {
                $ewma_acute = $current_load;
            } else {
                $ewma_acute = ( $alpha_acute * $current_load ) + ( ( 1 - $alpha_acute ) * $ewma_acute_prev );
            }
            $ewma_acute_prev = $ewma_acute;

            if ( $ewma_chronic_prev === null ) {
                $ewma_chronic = $current_load;
            } else {
                $ewma_chronic = ( $alpha_chronic * $current_load ) + ( ( 1 - $alpha_chronic ) * $ewma_chronic_prev );
            }
            $ewma_chronic_prev = $ewma_chronic;

            if ( $ewma_chronic > 0 ) {
                $ewma_acwr_values[$date_ymd] = round( $ewma_acute / $ewma_chronic, 2 );
            } else {
                $ewma_acwr_values[$date_ymd] = null;
            }
        }
        return $ewma_acwr_values;
    }
}

if ( ! function_exists( 'coachai_pd_calculate_monotony' ) ) {
    function coachai_pd_calculate_monotony( $daily_loads ) {
        $monotony_values = [];
        if (empty($daily_loads)) return $monotony_values;
        $sorted_dates = array_keys( $daily_loads );
        sort( $sorted_dates );
        $window_size = 7;

        for ( $i = 0; $i < count( $sorted_dates ); $i++ ) {
            $current_date_ymd = $sorted_dates[$i];
            $week_loads = [];

            for ( $j = 0; $j < $window_size; $j++ ) {
                if ( $i - $j >= 0 ) {
                    $date_key = $sorted_dates[$i - $j];
                    $week_loads[] = isset($daily_loads[$date_key]) ? $daily_loads[$date_key] : 0; // Yük yoksa 0
                } else {
                    // Yeterli veri yoksa, bu günü atla (veya null ata)
                    // $monotony_values[$current_date_ymd] = null; // Bunu döngü dışına taşı
                    // continue 2; // Dıştaki döngüyü de etkilememesi için
                    break; // İç döngüden çık
                }
            }

            if ( count( $week_loads ) === $window_size ) {
                $mean = array_sum( $week_loads ) / $window_size;
                if ($mean == 0) {
                    $monotony_values[$current_date_ymd] = 0;
                    continue;
                }

                $std_dev_sum_sq_diff = 0;
                foreach ( $week_loads as $load ) {
                    $std_dev_sum_sq_diff += pow( $load - $mean, 2 );
                }
                // Standart sapma için (N-1) yerine N kullanılıyor, bu bazı tanımlara göre değişebilir.
                // Genellikle popülasyon std dev için N, örneklem için N-1. Burada N (window_size) kullanılıyor.
                $variance = $std_dev_sum_sq_diff / $window_size;
                $std_dev = sqrt( $variance );

                if ( $std_dev > 0 ) {
                    $monotony_values[$current_date_ymd] = round( $mean / $std_dev, 2 );
                } else {
                    // Eğer std_dev 0 ise ve mean > 0 ise, bu mükemmel monotonluktur.
                    // Tanıma göre bu durumda monotonluk ya tanımsızdır ya da çok yüksek bir değerdir.
                    // Foster'ın orijinal makalesinde bu durum için belirli bir değer belirtilmemiş olabilir.
                    // Pratik bir yaklaşım olarak 0 (veya null) atanabilir ya da yüksek bir değer.
                    $monotony_values[$current_date_ymd] = ($mean > 0) ? $window_size : 0; // Örnek bir yaklaşım
                }
            } else {
                $monotony_values[$current_date_ymd] = null; // Haftalık veri tamamlanmadıysa null
            }
        }
        return $monotony_values;
    }
}


if ( ! function_exists( 'coachai_pd_calculate_strain' ) ) {
    function coachai_pd_calculate_strain( $daily_loads, $monotony_values ) {
        $strain_values = [];
        if (empty($daily_loads) || empty($monotony_values)) return $strain_values;
        $sorted_dates = array_keys( $daily_loads );
        sort( $sorted_dates );
        $window_size = 7;

        for ( $i = 0; $i < count( $sorted_dates ); $i++ ) {
            $current_date_ymd = $sorted_dates[$i];

            if ( isset( $monotony_values[$current_date_ymd] ) && $monotony_values[$current_date_ymd] !== null ) {
                $sum_last_7_days_load = 0;
                $count_days_for_sum = 0;
                for ( $j = 0; $j < $window_size; $j++ ) {
                    if ( $i - $j >= 0 ) {
                        $date_key = $sorted_dates[$i - $j];
                        $sum_last_7_days_load += isset($daily_loads[$date_key]) ? $daily_loads[$date_key] : 0;
                        $count_days_for_sum++;
                    } else {
                        break;
                    }
                }
                if ($count_days_for_sum === $window_size) { // Tam 7 günlük veri varsa
                    $strain_values[$current_date_ymd] = round( $sum_last_7_days_load * $monotony_values[$current_date_ymd] );
                } else {
                    $strain_values[$current_date_ymd] = null;
                }
            } else {
                $strain_values[$current_date_ymd] = null;
            }
        }
        return $strain_values;
    }
}


if ( ! function_exists( 'coachai_pd_calculate_acwr_for_athlete_today' ) ) {
    function coachai_pd_calculate_acwr_for_athlete_today( $athlete_id, $current_user_id ) {
        $wp_timezone = wp_timezone();
        $today = new DateTime('now', $wp_timezone);
        // EWMA için yeterli geçmiş veri sağlamak amacıyla daha geniş bir aralık çekilir.
        // Genellikle 28 günlük kronik pencere için en az 28 gün + birkaç hafta daha iyi sonuç verir.
        $start_date = (clone $today)->modify('-59 days'); // Yaklaşık 2 ay + buffer

        $daily_loads = coachai_pd_get_daily_training_loads_for_athlete(
            $athlete_id,
            $start_date->format('Ymd'),
            $today->format('Ymd'),
            $current_user_id
        );

        if (empty($daily_loads)) return null;

        // daily_loads dizisinin $start_date'den $today'e kadar tüm günleri içermesini sağla (0 yük ile)
        $filled_daily_loads = [];
        $loop_date = clone $start_date;
        while($loop_date <= $today) {
            $ymd = $loop_date->format('Ymd');
            $filled_daily_loads[$ymd] = isset($daily_loads[$ymd]) ? $daily_loads[$ymd] : 0;
            $loop_date->modify('+1 day');
        }

        $alpha_acute = 2 / (7 + 1);   // 7 günlük akut pencere için
        $alpha_chronic = 2 / (28 + 1); // 28 günlük kronik pencere için

        $ewma_acwr_values = coachai_pd_calculate_ewma_acwr($filled_daily_loads, $alpha_acute, $alpha_chronic);

        $today_ymd = $today->format('Ymd');
        return isset($ewma_acwr_values[$today_ymd]) ? $ewma_acwr_values[$today_ymd] : null;
    }
}

if ( ! function_exists( 'coachai_pd_calculate_acwr_for_athlete' ) ) {
    function coachai_pd_calculate_acwr_for_athlete($athlete_id, $current_user_id) {
        return coachai_pd_calculate_acwr_for_athlete_today($athlete_id, $current_user_id);
    }
}

if ( ! function_exists( 'coachai_pd_prepare_ai_team_analysis_data' ) ) {
    function coachai_pd_prepare_ai_team_analysis_data( $team_id, $current_user_id, $current_lang = 'en' ) {
        $output_data_string = "";
        $output_data_string .= ($current_lang === 'tr' ? "Takım Analizi Verileri:\n" : "Team Analysis Data:\n");
        $wp_timezone = wp_timezone();
        $end_date = new DateTime('now', $wp_timezone);
        $start_date_30_days = (clone $end_date)->modify('-29 days'); // Son 30 gün

        $team_athletes = coachai_pd_get_team_athletes_list( $team_id, $current_lang );
        if (empty($team_athletes) || count($team_athletes) <=1 ) {
            return ($current_lang === 'tr' ? "Takımda analiz edilecek sporcu bulunmuyor.\n" : "No athletes in the team to analyze.\n");
        }

        $output_data_string .= ($current_lang === 'tr' ? "\nSporcu Antrenman Yükü Verileri (Son 30 Gün):\n" : "\nAthlete Training Load Data (Last 30 Days):\n");

        foreach ( $team_athletes as $athlete ) {
            if ($athlete['id'] === 'team_average') continue;

            $athlete_id = $athlete['id'];
            $athlete_name = $athlete['name'];
            // Son 30 gün için günlük yükleri al
            $daily_loads_30_days = coachai_pd_get_daily_training_loads_for_athlete(
                $athlete_id,
                $start_date_30_days->format('Ymd'),
                $end_date->format('Ymd'),
                $current_user_id
            );

            $total_tl_30_days = array_sum($daily_loads_30_days);
            $num_active_days_30 = count(array_filter($daily_loads_30_days, function($load) { return $load > 0; }));
            $avg_daily_tl_30_days = $num_active_days_30 > 0 ? round($total_tl_30_days / $num_active_days_30) : 0;

            // Haftalık yükler için son 4 haftanın verisini al
            $start_date_for_weeks = (clone $end_date)->modify('-27 days'); // Son 4 haftanın başlangıcı
             $all_loads_for_weeks = coachai_pd_get_daily_training_loads_for_athlete(
                $athlete_id,
                $start_date_for_weeks->format('Ymd'),
                $end_date->format('Ymd'),
                $current_user_id
            );


            $weekly_tl = array_fill(0, 4, 0); // Son 4 hafta için
            for ($w = 0; $w < 4; $w++) { // 0: en son hafta, 1: bir önceki hafta ...
                $week_sum = 0;
                for ($d = 0; $d < 7; $d++) {
                    // Mevcut haftanın son gününden ($end_date) geriye doğru sayarak haftaları hesapla
                    $day_in_week = (clone $end_date)->modify('-' . ($w * 7 + $d) . ' days')->format('Ymd');
                     if (isset($all_loads_for_weeks[$day_in_week])) {
                        $week_sum += $all_loads_for_weeks[$day_in_week];
                    }
                }
                $weekly_tl[$w] = $week_sum;
            }
            // $weekly_tl[0] en son tamamlanan hafta, $weekly_tl[1] ondan önceki hafta

            $output_data_string .= "- " . $athlete_name . ":\n";
            $output_data_string .= "  " . ($current_lang === 'tr' ? "Son 30 Gün Aktif Gün Ort. Ant. Yükü: " : "Last 30 Days Avg. Daily TL (Active Days): ") . $avg_daily_tl_30_days . " AU\n";

            if (isset($weekly_tl[0]) && isset($weekly_tl[1])) { // Son iki haftanın verisi varsa karşılaştır
                if ($weekly_tl[1] > 0) { // Önceki hafta yükü 0'dan büyükse
                    $change_last_week = round((($weekly_tl[0] - $weekly_tl[1]) / $weekly_tl[1]) * 100, 1);
                    $output_data_string .= "  " . ($current_lang === 'tr' ? "Son Hafta TL Değişimi (önceki haftaya göre): " : "Last Week TL Change (vs prev. week): ") . $change_last_week . "%\n";
                    if (abs($change_last_week) > 10) {
                         $output_data_string .= "    " . ($current_lang === 'tr' ? "UYARI: Haftalık yükte %10'dan fazla değişim!\n" : "WARNING: Weekly load changed by more than 10%!\n");
                    }
                } elseif ($weekly_tl[0] > 0) { // Önceki hafta 0, bu hafta yük varsa
                    $output_data_string .= "  " . ($current_lang === 'tr' ? "Son Hafta TL Değişimi (önceki haftaya göre): " : "Last Week TL Change (vs prev. week): ") . "100%+ (from 0)\n";
                }
            }
        }

        $recent_notes_for_ai = coachai_pd_get_recent_notes( $team_id, $current_user_id, $current_lang, 20, 'team_average' ); // Takım için tüm notlar
        $output_data_string .= ($current_lang === 'tr' ? "\nSon Notlar (Son 7 Gün):\n" : "\nRecent Notes (Last 7 Days):\n");
        $notes_found_last_7_days = false;
        $seven_days_ago = (new DateTime('now', $wp_timezone))->modify('-7 days');

        if (!empty($recent_notes_for_ai)) {
            foreach ($recent_notes_for_ai as $note) {
                try {
                    $note_date_obj = new DateTime($note['note_date_raw'], $wp_timezone);
                    if ($note_date_obj >= $seven_days_ago) {
                        $output_data_string .= "- [" . $note['note_date'] . "] " . $note['athlete_name'] . ": " . strip_tags($note['full_note_content']) . "\n";
                        $notes_found_last_7_days = true;
                        $injury_keywords_tr = ['burktu', 'sakat', 'ağrı', 'şişlik', 'ödem', ' प्रॉब्लम', ' समस्या'];
                        $injury_keywords_en = ['sprain', 'injur', 'pain', 'swell', 'ache', 'problem', 'issue'];
                        $keywords_to_check = ($current_lang === 'tr') ? $injury_keywords_tr : $injury_keywords_en;
                        $pattern = '/\b(' . implode('|', $keywords_to_check) . ')\b/i';

                        if (preg_match($pattern, strip_tags(strtolower($note['full_note_content'])))) {
                            $output_data_string .= "    " . ($current_lang === 'tr' ? "UYARI: Notta potansiyel sakatlık/ağrı belirtisi (" . $note['athlete_name'] . ")!\n" : "WARNING: Note indicates potential injury/pain (" . $note['athlete_name'] . ")!\n");
                        }
                    }
                } catch (Exception $e) {
                     error_log("Date parsing error for note date '{$note['note_date_raw']}': " . $e->getMessage());
                }
            }
        }
        if (!$notes_found_last_7_days) {
            $output_data_string .= ($current_lang === 'tr' ? "Son 7 günde not bulunamadı.\n" : "No notes found in the last 7 days.\n");
        }

        $output_data_string .= ($current_lang === 'tr' ? "\nLütfen bu verilere dayanarak takım hakkında genel bir değerlendirme, potansiyel riskler ve öneriler sunun.\n" : "\nPlease provide a general assessment of the team, potential risks, and recommendations based on this data.\n");

        return $output_data_string;
    }
}

// GÜNCELLENDİ: coachai_pd_get_assigned_program_view_data
if ( ! function_exists( 'coachai_pd_get_assigned_program_view_data' ) ) {
    function coachai_pd_get_assigned_program_view_data( $program_id, $session_ref_id, $current_user_id, $current_lang = 'en' ) {
        $program_post = get_post($program_id);
        $session_post = get_post($session_ref_id);

        if (!$program_post || $program_post->post_type !== 'workout' || !$session_post || $session_post->post_type !== 'training_session') {
            return null;
        }

        $data = [
            'program_title' => get_the_title($program_id),
            'session_title' => get_the_title($session_ref_id),
            'session_date'  => get_field(ACF_SESSION_FIELD_KEY_DATE, $session_ref_id),
            'exercises'     => [],
            'participants_rpe' => [],
        ];

        if (have_rows(ACF_WORKOUT_FIELD_EXERCISE_LIST, $program_id)) {
            while (have_rows(ACF_WORKOUT_FIELD_EXERCISE_LIST, $program_id)) {
                the_row();
                // Kullanılacak tahmini alan adları: 'exercise_name', 'exercise_image', 'intensity', 'set_count' (veya 'set'), 'reps', 'notes'
                $exercise_image_data = get_sub_field('exercise_image');
                $data['exercises'][] = [
                    'name'      => get_sub_field('exercise_name'),
                    'image_url' => $exercise_image_data ? ($exercise_image_data['sizes']['thumbnail'] ?? $exercise_image_data['url']) : '',
                    'intensity' => get_sub_field('intensity'),
                    'sets'      => get_sub_field('set_count'), // ACF'deki alan adını kontrol edin ('set' veya 'set_count')
                    'reps'      => get_sub_field('reps'),
                    'notes'     => get_sub_field('notes'),
                ];
            }
        }

        $rpe_repeater = get_field(ACF_SESSION_RPE_REPEATER_KEY, $session_ref_id);
        $session_participants_raw = get_field(ACF_SESSION_FIELD_KEY_PARTICIPANTS, $session_ref_id);
        $session_participant_ids = [];
        if ( ! empty( $session_participants_raw ) ) {
            $participants_array = is_array( $session_participants_raw ) ? $session_participants_raw : array( $session_participants_raw );
            foreach ( $participants_array as $p_obj_or_id ) {
                if ( $p_obj_or_id instanceof WP_Post ) $session_participant_ids[] = $p_obj_or_id->ID;
                elseif ( is_numeric( $p_obj_or_id ) ) $session_participant_ids[] = intval( $p_obj_or_id );
                elseif (is_array($p_obj_or_id) && isset($p_obj_or_id['ID'])) $session_participant_ids[] = intval($p_obj_or_id['ID']);
            }
        }

        if (is_array($rpe_repeater)) {
            foreach ($rpe_repeater as $rpe_entry) {
                $p_obj_acf = $rpe_entry[ACF_SESSION_SUB_FIELD_NAME_RPE_PARTICIPANT] ?? null;
                $rpe_val_acf = $rpe_entry[ACF_SESSION_SUB_FIELD_NAME_RPE_VALUE] ?? null;
                $duration_val_acf = $rpe_entry[ACF_SESSION_SUB_FIELD_NAME_RPE_ACTUAL_DURATION] ?? null;

                $p_id = 0;
                if ($p_obj_acf instanceof WP_Post) $p_id = $p_obj_acf->ID;
                elseif (is_numeric($p_obj_acf)) $p_id = intval($p_obj_acf);
                elseif (is_array($p_obj_acf) && isset($p_obj_acf['ID'])) $p_id = intval($p_obj_acf['ID']);

                if ($p_id > 0 && in_array($p_id, $session_participant_ids)) {
                    $p_first_name = get_field(ACF_ATHLETE_FIELD_KEY_FIRST_NAME, $p_id);
                    $p_last_name = get_field(ACF_ATHLETE_FIELD_KEY_LAST_NAME, $p_id);
                    $p_display_name = trim($p_first_name . ' ' . $p_last_name);
                    if (empty($p_display_name)) $p_display_name = get_the_title($p_id);

                    $data['participants_rpe'][] = [
                        'name'     => $p_display_name,
                        'rpe'      => $rpe_val_acf ?? '-',
                        'duration' => $duration_val_acf ?? '-',
                    ];
                }
            }
        }
        return $data;
    }
}

// GÜNCELLENDİ: coachai_pd_get_athlete_note_view_data
if ( ! function_exists( 'coachai_pd_get_athlete_note_view_data' ) ) {
    function coachai_pd_get_athlete_note_view_data( $athlete_id, $note_unique_id, $current_user_id, $current_lang = 'en' ) {
        $parts = explode('_row_', $note_unique_id);
        if (count($parts) !== 2 || !is_numeric($parts[1])) {
            return null;
        }
        $note_row_index_target = intval($parts[1]);
        $wp_timezone = wp_timezone();
        $athlete_post = get_post($athlete_id);
        if (!$athlete_post || $athlete_post->post_type !== 'athlete') {
            return null;
        }

        $note_data = null;
        if (have_rows(ACF_ATHLETE_NOTES_REPEATER_KEY, $athlete_id)) {
            $current_row_iter = 0;
            while (have_rows(ACF_ATHLETE_NOTES_REPEATER_KEY, $athlete_id)) {
                the_row();
                $current_row_iter++;

                if ($current_row_iter === $note_row_index_target) {
                    // Kullanılacak tahmini alan adları: 'note_date', 'note_session_link', 'note_content', 'note_context'
                    $note_date_raw = get_sub_field('note_date');
                    $note_session_obj = get_sub_field('note_session_link');
                    $note_content_val = get_sub_field('note_content');
                    $note_context_val_raw = get_sub_field('note_context');
                    $note_context_val = is_array($note_context_val_raw) ? ($note_context_val_raw['label'] ?? $note_context_val_raw['value'] ?? '') : $note_context_val_raw;

                    $athlete_first_name = get_field(ACF_ATHLETE_FIELD_KEY_FIRST_NAME, $athlete_id);
                    $athlete_last_name = get_field(ACF_ATHLETE_FIELD_KEY_LAST_NAME, $athlete_id);
                    $athlete_display_name = trim($athlete_first_name . ' ' . $athlete_last_name);
                    if (empty($athlete_display_name)) $athlete_display_name = get_the_title($athlete_id);

                    $note_data = [
                        'athlete_id'           => $athlete_id,
                        'athlete_name'         => $athlete_display_name,
                        'note_content'         => $note_content_val,
                        'note_date_raw'        => $note_date_raw,
                        'note_date'            => $note_date_raw ? date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($note_date_raw)) : '-',
                        'note_context'         => $note_context_val,
                        'related_session_id'   => ($note_session_obj instanceof WP_Post) ? $note_session_obj->ID : null,
                        'related_session_title'=> ($note_session_obj instanceof WP_Post) ? get_the_title($note_session_obj->ID) : null,
                        'related_session_date' => ($note_session_obj instanceof WP_Post) ? get_field(ACF_SESSION_FIELD_KEY_DATE, $note_session_obj->ID) : null,
                        'historical_acwr'      => null,
                        'historical_monotony'  => null,
                    ];

                    if ($note_date_raw) {
                        try {
                            $note_date_obj = new DateTime($note_date_raw, $wp_timezone);
                            $end_period_for_calc = $note_date_obj->format('Ymd');
                            $start_period_for_calc = (clone $note_date_obj)->modify('-35 days')->format('Ymd'); // EWMA için ~5 hafta

                            $historical_daily_loads = coachai_pd_get_daily_training_loads_for_athlete($athlete_id, $start_period_for_calc, $end_period_for_calc, $current_user_id);

                            $filled_historical_loads = [];
                            $loop_h_date_start = new DateTime($start_period_for_calc, $wp_timezone);
                            $loop_h_date_end = new DateTime($end_period_for_calc, $wp_timezone);

                            while($loop_h_date_start <= $loop_h_date_end) {
                                $ymd = $loop_h_date_start->format('Ymd');
                                $filled_historical_loads[$ymd] = isset($historical_daily_loads[$ymd]) ? $historical_daily_loads[$ymd] : 0;
                                $loop_h_date_start->modify('+1 day');
                            }

                            if (!empty($filled_historical_loads)) {
                                $alpha_acute = 2 / (7 + 1);
                                $alpha_chronic = 2 / (28 + 1);
                                $ewma_acwr_hist = coachai_pd_calculate_ewma_acwr($filled_historical_loads, $alpha_acute, $alpha_chronic);
                                if (isset($ewma_acwr_hist[$end_period_for_calc])) {
                                    $note_data['historical_acwr'] = $ewma_acwr_hist[$end_period_for_calc];
                                }

                                $monotony_hist = coachai_pd_calculate_monotony($filled_historical_loads);
                                if (isset($monotony_hist[$end_period_for_calc])) {
                                    $note_data['historical_monotony'] = $monotony_hist[$end_period_for_calc];
                                }
                            }
                        } catch (Exception $e) {
                            error_log("Error calculating historical metrics for note: " . $e->getMessage());
                        }
                    }
                    break;
                }
            }
        }
        return $note_data;
    }
}

// --- AJAX HANDLERS ---
if ( ! function_exists( 'coachai_ajax_get_assigned_workouts' ) ) {
    add_action( 'wp_ajax_pd_get_assigned_workouts_ajax', 'coachai_ajax_get_assigned_workouts' );
    function coachai_ajax_get_assigned_workouts() {
        check_ajax_referer( 'coachai_player_dashboard_nonce', '_ajax_nonce' );
        $current_user_id = get_current_user_id();
        $current_lang    = isset( $_POST['lang'] ) ? sanitize_text_field( $_POST['lang'] ) : 'en';
        $player_id_param = isset( $_POST['player_id'] ) ? $_POST['player_id'] : null;
        $player_id       = ($player_id_param === 'team_average' || $player_id_param === 'null' || $player_id_param === '') ? null : intval( $player_id_param );
        $count           = isset( $_POST['count'] ) ? intval( $_POST['count'] ) : 5;
        $team_id         = function_exists('coachai_get_current_coach_team_id') ? coachai_get_current_coach_team_id($current_user_id, false) : null;

        $pd_td_ajax = [
            'tr' => ['no_assigned_workouts' => 'Henüz atanmış bir kuvvet programı bulunmuyor.', 'team_label' => 'TAKIM', 'individual_label' => 'BİREYSEL', 'view_details' => 'Detaylar'],
            'en' => ['no_assigned_workouts' => 'No assigned strength programs found yet.', 'team_label' => 'TEAM', 'individual_label' => 'INDIVIDUAL', 'view_details' => 'Details']
        ];
        $td_ajax = $pd_td_ajax[$current_lang] ?? $pd_td_ajax['en'];

        $workouts = coachai_pd_get_last_assigned_workouts( $team_id, $current_user_id, $current_lang, $count, $player_id );
        $html = '<ul class="assigned-programs-list">';
        if ( ! empty( $workouts ) ) {
            $assigned_program_view_slug_ajax = defined('COACHAI_ASSIGNED_PROGRAM_VIEW_SLUG') ? COACHAI_ASSIGNED_PROGRAM_VIEW_SLUG : 'assigned-program-view';
            foreach ( $workouts as $workout_entry ) {
                $session_date_formatted = $workout_entry['session_date'] ? date_i18n( get_option('date_format'), strtotime( $workout_entry['session_date'] ) ) : '-';
                $start_time_formatted = $workout_entry['session_start_time'] ? date_i18n( get_option('time_format'), strtotime( $workout_entry['session_start_time'] ) ) : '';
                $program_view_params = 'id=' . $workout_entry['assigned_program_id'] . '&session_ref=' . $workout_entry['session_id'];

                $html .= '<li class="assigned-program-item coachai-shell-navigate-js" data-target-page="' . esc_attr($assigned_program_view_slug_ajax) . '" data-target-params="' . esc_attr($program_view_params) . '">';
                $html .= '<div class="program-item-main">';
                $html .= '<h5 class="program-title">' . esc_html( $workout_entry['assigned_program_title'] ) . '</h5>';
                $html .= '<span class="program-date">' . esc_html( $session_date_formatted . ($start_time_formatted ? ' (' . $start_time_formatted . ')' : '') ) . '</span>';
                $html .= '</div>';
                $html .= '<div class="program-item-meta">';
                $html .= '<span class="session-type-badge session-type-' . esc_attr($workout_entry['session_type']) . '">';
                $is_team_session = ($workout_entry['session_type'] === 'team' || $workout_entry['session_type'] === 'takım');
                $html .= esc_html( $is_team_session ? $td_ajax['team_label'] : $td_ajax['individual_label'] );
                $html .= '</span>';
                $html .= '<span class="program-participants">' . esc_html( $workout_entry['participants_display'] ) . '</span>';
                $html .= '</div>';
                $html .= '</li>';
            }
        } else {
            $html .= '<li class="no-programs-found"><p class="placeholder-text">' . esc_html($td_ajax['no_assigned_workouts']) . '</p></li>';
        }
        $html .= '</ul>';
        wp_send_json_success( array( 'html' => $html ) );
    }
}

if ( ! function_exists( 'coachai_ajax_get_recent_notes' ) ) {
    add_action( 'wp_ajax_pd_get_recent_notes_ajax', 'coachai_ajax_get_recent_notes' );
    function coachai_ajax_get_recent_notes() {
        check_ajax_referer( 'coachai_player_dashboard_nonce', '_ajax_nonce' );
        $current_user_id = get_current_user_id();
        $current_lang    = isset( $_POST['lang'] ) ? sanitize_text_field( $_POST['lang'] ) : 'en';
        $player_id_param = isset( $_POST['player_id'] ) ? $_POST['player_id'] : null;
        $player_id_for_query = ($player_id_param === 'team_average' || $player_id_param === 'null' || $player_id_param === '') ? 'team_average' : intval( $player_id_param );
        $count           = isset( $_POST['count'] ) ? intval( $_POST['count'] ) : ($player_id_for_query === 'team_average' ? 15 : 10);
        $team_id         = function_exists('coachai_get_current_coach_team_id') ? coachai_get_current_coach_team_id($current_user_id, false) : null;

        global $i18n_player_dashboard; // Bu değişkenin sayfa şablonunda tanımlandığını varsayıyoruz
        $i18n_notes_pd_ajax = ($i18n_player_dashboard[$current_lang]['notes_i18n'] ?? ($i18n_player_dashboard['en']['notes_i18n'] ?? [
            'no_recent_notes' => 'No recent notes found.',
            'view_note_details' => 'View',
            'delete_button' => 'Delete',
            'confirm_delete_note' => 'Are you sure you want to delete this note?'
        ]));

        $notes = coachai_pd_get_recent_notes( $team_id, $current_user_id, $current_lang, $count, $player_id_for_query );
        $html = '';
        if ( ! empty( $notes ) ) {
            $athlete_note_view_slug_ajax = defined('COACHAI_ATHLETE_NOTE_VIEW_SLUG') ? COACHAI_ATHLETE_NOTE_VIEW_SLUG : 'athlete-note-view';
            foreach ( $notes as $note_entry ) {
                $note_view_params = 'note_id=' . urlencode($note_entry['note_unique_id']) . '&athlete_id=' . $note_entry['athlete_id'] . '&from=player-dashboard';
                $html .= '<li class="note-list-item coachai-shell-navigate-js" data-target-page="' . esc_attr($athlete_note_view_slug_ajax) . '" data-target-params="' . esc_attr($note_view_params) . '">';
                $html .= '<div class="note-content-wrapper">';
                $html .= '<span class="note-meta">';
                $html .= esc_html( $note_entry['note_meta_display'] ?? ($note_entry['athlete_name'] . ' - ' . $note_entry['note_date']) );
                if (!empty($note_entry['note_context'])) {
                    $html .= '<em class="note-context"> (' . esc_html(ucfirst($note_entry['note_context'])) . ')</em>';
                }
                $html .= '</span>';
                $html .= '<p class="note-excerpt">' . esc_html( $note_entry['note_excerpt'] ) . '</p>';
                $html .= '</div>';
                $html .= '<div class="note-actions">';
                if ($player_id_param && $player_id_param !== 'team_average' && $player_id_param == $note_entry['athlete_id']) {
                    $html .= '<button class="coachai-btn coachai-btn-danger coachai-btn-icon-only coachai-btn-xs delete-note-btn-pd" ';
                    $html .= 'data-note-id="' . esc_attr($note_entry['note_unique_id']) . '" ';
                    $html .= 'data-athlete-id="' . esc_attr($note_entry['athlete_id']) . '" ';
                    $html .= 'title="' . esc_attr($i18n_notes_pd_ajax['delete_button']) . '" ';
                    $html .= 'onclick="event.stopPropagation(); /* JS will handle deletion */">';
                    $html .= '<i class="fas fa-trash-alt"></i></button>';
                }
                $html .= '</div>';
                $html .= '</li>';
            }
        } else {
            $html = '<li class="no-notes-found placeholder-text">' . esc_html($i18n_notes_pd_ajax['no_recent_notes']) . '</li>';
        }
        wp_send_json_success( array( 'html' => $html ) );
    }
}

add_action( 'wp_ajax_pd_get_ai_team_analysis_ajax', 'coachai_ajax_get_ai_team_analysis' );
function coachai_ajax_get_ai_team_analysis() {
    check_ajax_referer( 'coachai_player_dashboard_nonce', '_ajax_nonce' );
    $current_user_id = get_current_user_id();
    $current_lang    = isset( $_POST['lang'] ) ? sanitize_text_field( $_POST['lang'] ) : 'en';
    $team_id         = function_exists('coachai_get_current_coach_team_id') ? coachai_get_current_coach_team_id($current_user_id, false) : null;

    if (!function_exists('coachai_can_user_access_feature') || !coachai_can_user_access_feature('pro', $current_user_id)) {
        wp_send_json_error(['message' => ($current_lang === 'tr' ? 'Bu özellik Pro/Ultimate plan gerektirir.' : 'This feature requires a Pro/Ultimate plan.')]);
        return;
    }
    if (!$team_id) {
        wp_send_json_error(['message' => ($current_lang === 'tr' ? 'Takım yapılandırılmamış.' : 'No team configured.')]);
        return;
    }
    $cache_key = 'coachai_ai_team_analysis_' . $team_id . '_' . $current_lang;
    $cached_analysis = get_transient($cache_key);

    if ($cached_analysis !== false && is_string($cached_analysis) && is_serialized($cached_analysis)) { // Önbellek kontrolü
        wp_send_json_success(unserialize($cached_analysis));
        return;
    }

    $ai_input_data_string = '';
    if (function_exists('coachai_pd_prepare_ai_team_analysis_data')) {
        $ai_input_data_string = coachai_pd_prepare_ai_team_analysis_data($team_id, $current_user_id, $current_lang);
    }
    if (empty($ai_input_data_string) || str_word_count(strip_tags($ai_input_data_string)) < 20 ) { // Yetersiz veri kontrolü
        wp_send_json_error(['message' => ($current_lang === 'tr' ? 'Yapay zeka analizi oluşturmak için yeterli veri yok.' : 'Not enough data to generate AI analysis.')]);
        return;
    }
    if ( !defined( 'DEEPSEEK_API_KEY' ) || empty(DEEPSEEK_API_KEY) || !defined( 'DEEPSEEK_BASE_URL' ) || empty(DEEPSEEK_BASE_URL) ) {
        wp_send_json_error( ['message' => ($current_lang === 'tr' ? 'DeepSeek API detayları yapılandırılmamış.' : 'DeepSeek API details are not configured.') ], 500 );
        return;
    }

    $system_prompt_ai_team_tr = "Sen bir deneyimli spor antrenörüsün. Aşağıdaki takım verilerini analiz et:\n{VERI}\n\nBu verilere dayanarak, takımın genel durumu, bireysel sporcular için potansiyel riskler (özellikle haftalık yük değişimleri %10'dan fazlaysa veya notlarda sakatlık/ağrı belirtileri varsa) ve antrenör için pratik, uygulanabilir öneriler sun. Cevabını Markdown formatında, kısa ve öz paragraflar veya madde işaretleri kullanarak düzenle. Özellikle dikkat edilmesi gereken sporcuları vurgula.";
    $system_prompt_ai_team_en = "You are an experienced sports coach. Analyze the following team data:\n{DATA}\n\nBased on this data, provide a general assessment of the team, potential risks for individual athletes (especially if weekly load changes exceed 10% or notes indicate injury/pain), and practical, actionable recommendations for the coach. Format your response in Markdown using concise paragraphs or bullet points. Highlight athletes that require special attention.";
    $current_system_prompt = ($current_lang === 'tr') ? $system_prompt_ai_team_tr : $system_prompt_ai_team_en;
    $final_prompt_for_ai = str_replace(($current_lang === 'tr' ? '{VERI}' : '{DATA}'), $ai_input_data_string, $current_system_prompt);

    $api_url = trailingslashit( DEEPSEEK_BASE_URL ) . 'chat/completions';
    $request_body = [
        'model'       => 'deepseek-chat',
        'messages'    => [
            [ 'role' => 'user', 'content' => $final_prompt_for_ai ],
        ],
        'max_tokens'  => 1500,
        'temperature' => 0.6,
    ];
    $response = wp_remote_post( $api_url, [
        'method'  => 'POST',
        'headers' => ['Content-Type' => 'application/json', 'Accept' => 'application/json', 'Authorization' => 'Bearer ' . DEEPSEEK_API_KEY],
        'body'    => wp_json_encode( $request_body ),
        'timeout' => 90,
    ]);

    if ( is_wp_error( $response ) ) {
        wp_send_json_error( ['message' => ($current_lang === 'tr' ? 'Yapay zeka API bağlantı hatası: ' : 'AI API connection error: ') . $response->get_error_message()], 503 );
        return;
    }
    $response_code = wp_remote_retrieve_response_code( $response );
    $response_body_str = wp_remote_retrieve_body( $response );
    $response_body = json_decode( $response_body_str, true );

    if ( $response_code !== 200 || empty( $response_body['choices'][0]['message']['content'] ) ) {
        error_log("AI Team Analysis DeepSeek Error (Code {$response_code}): " . $response_body_str);
        wp_send_json_error( ['message' => ($current_lang === 'tr' ? 'Yapay zekadan geçerli bir yanıt alınamadı.' : 'Could not get a valid response from AI.') . " (Status: {$response_code})"], 500 );
        return;
    }

    $ai_generated_text = $response_body['choices'][0]['message']['content'];
    $ai_response_html = class_exists('Parsedown') ? (new Parsedown())->text( $ai_generated_text ) : nl2br( esc_textarea( $ai_generated_text ) );
    $last_updated_time = date_i18n( get_option('date_format') . ' ' . get_option('time_format'), current_time('timestamp') );

    $data_to_send_and_cache = serialize([
        'analysis_html' => $ai_response_html,
        'last_updated'  => $last_updated_time,
    ]);
    set_transient($cache_key, $data_to_send_and_cache, 6 * HOUR_IN_SECONDS );
    wp_send_json_success( unserialize($data_to_send_and_cache) );
}

if ( ! function_exists( 'coachai_pd_get_daily_team_average_training_loads' ) ) {
    function coachai_pd_get_daily_team_average_training_loads( $team_id, $start_date_ymd, $end_date_ymd, $current_user_id, $current_lang = 'en' ) {
        $team_daily_average_loads = [];
        $wp_timezone = wp_timezone();
        $athletes = coachai_pd_get_team_athletes_list( $team_id, $current_lang );

        try {
            $start_dt = new DateTime($start_date_ymd, $wp_timezone);
            $end_dt = new DateTime($end_date_ymd, $wp_timezone);
        } catch (Exception $e) {
            error_log("Date parsing error in coachai_pd_get_daily_team_average_training_loads: " . $e->getMessage());
            return $team_daily_average_loads; // Return empty on date error
        }


        if ( empty( $athletes ) || count($athletes) <=1 ) { // <=1 because the first entry is 'team_average'
            $current_loop_dt = clone $start_dt;
            while($current_loop_dt <= $end_dt){
                $team_daily_average_loads[$current_loop_dt->format('Ymd')] = 0;
                $current_loop_dt->modify('+1 day');
            }
            return $team_daily_average_loads;
        }

        $all_players_daily_loads_sum = [];
        $player_count_per_day = [];

        // Initialize date range for sums and counts
        $current_loop_dt_init = clone $start_dt;
        while($current_loop_dt_init <= $end_dt){
            $date_key_init = $current_loop_dt_init->format('Ymd');
            $all_players_daily_loads_sum[$date_key_init] = 0;
            $player_count_per_day[$date_key_init] = 0;
             $team_daily_average_loads[$date_key_init] = 0; // Initialize average as 0
            $current_loop_dt_init->modify('+1 day');
        }


        foreach ( $athletes as $athlete ) {
            if ( $athlete['id'] === 'team_average' ) continue;

            $player_id = $athlete['id'];
            $player_daily_loads = coachai_pd_get_daily_training_loads_for_athlete( $player_id, $start_date_ymd, $end_date_ymd, $current_user_id );

            foreach ( $player_daily_loads as $date => $load ) {
                 if (isset($all_players_daily_loads_sum[$date])) { // Ensure date is within the initialized range
                    if ($load > 0) {
                        $all_players_daily_loads_sum[$date] += $load;
                        $player_count_per_day[$date]++;
                    }
                }
            }
        }

        $current_loop_dt_avg = clone $start_dt;
        while($current_loop_dt_avg <= $end_dt){
            $date_key = $current_loop_dt_avg->format('Ymd');
            if ( isset( $all_players_daily_loads_sum[$date_key] ) && isset( $player_count_per_day[$date_key] ) && $player_count_per_day[$date_key] > 0 ) {
                $team_daily_average_loads[$date_key] = round( $all_players_daily_loads_sum[$date_key] / $player_count_per_day[$date_key] );
            }
            // If no players trained or no load, it remains 0 (initialized above)
            $current_loop_dt_avg->modify('+1 day');
        }
        return $team_daily_average_loads;
    }
}

if (!function_exists('coachai_la_get_player_list_for_dock')) {
    function coachai_la_get_player_list_for_dock( $team_id, $current_lang = 'en' ) {
        $athletes_data = array(
            'post_type'      => 'athlete',
            'posts_per_page' => -1,
            'meta_query'     => array(
                array(
                    'key'     => ACF_FIELD_NAME_ATHLETE_TEAM, // Sabit kullanılıyor
                    'value'   => $team_id,
                    'compare' => '=',
                ),
            ),
            'orderby'        => 'title',
            'order'          => 'ASC',
            'post_status'    => 'publish',
            'lang'           => $current_lang,
        );
        $athletes_query = new WP_Query( $athletes_args );
        if ( $athletes_query->have_posts() ) {
            while ( $athletes_query->have_posts() ) {
                $athletes_query->the_post();
                $athlete_id         = get_the_ID();
                $athlete_first_name = get_field( ACF_ATHLETE_FIELD_KEY_FIRST_NAME, $athlete_id ); // Sabit kullanılıyor
                if ( empty( $athlete_first_name ) ) {
                    $athlete_first_name = strtok(get_the_title($athlete_id), " ");
                }
                if ( empty( $athlete_first_name ) || $athlete_first_name === 'Otomatik Taslak' || $athlete_first_name === 'Auto Draft') {
                    $athlete_first_name = ($current_lang === 'tr' ? 'Sporcu' : 'Athlete') . ' #' . $athlete_id;
                }
                $profile_pic_data = get_field( ACF_ATHLETE_FIELD_KEY_PROFILE_PICTURE, $athlete_id ); // Sabit kullanılıyor
                $profile_pic_url  = 'https://ui-avatars.com/api/?name=' . urlencode( $athlete_first_name ) . '&size=40&background=e0e7ff&color=4338ca&font-size=0.4&bold=true';
                if ( $profile_pic_data && is_array( $profile_pic_data ) && isset( $profile_pic_data['sizes']['thumbnail'] ) ) {
                    $profile_pic_url = $profile_pic_data['sizes']['thumbnail'];
                } elseif ( $profile_pic_data && is_array( $profile_pic_data ) && isset( $profile_pic_data['url'] ) ) {
                    $profile_pic_url = $profile_pic_data['url'];
                } elseif ( is_numeric( $profile_pic_data ) ) {
                    $profile_pic_url_temp = wp_get_attachment_image_url( $profile_pic_data, 'thumbnail' );
                    if ( $profile_pic_url_temp ) {
                        $profile_pic_url = $profile_pic_url_temp;
                    }
                }
                $athletes_data[] = array(
                    'id'         => $athlete_id,
                    'first_name' => $athlete_first_name,
                    'avatar_url' => $profile_pic_url,
                );
            }
            wp_reset_postdata();
        }
        return $athletes_data;
    }
}
if ( ! function_exists( 'coachai_la_get_long_term_chart_data' ) ) {
    function coachai_la_get_long_term_chart_data( $athlete_id, $days_period, $current_user_id ) {
        $output = [
            'labels'    => [],
            'daily_tl'  => [],
            'ra_acwr'   => [],
            'ewma_acwr' => [],
        ];
        $wp_timezone = wp_timezone();
        $today = new DateTime("now", $wp_timezone);
        // EWMA/RA ACWR için kronik pencere (28 gün) + akut pencere (7 gün) + grafikte gösterilecek periyot için yeterli veri
        // En az 28 gün + grafikteki periyot kadar geçmiş veri gerekli olabilir.
        // Buffer ekleyerek (örn. +7 gün) hesaplamaların başlangıçta daha stabil olmasını sağlayabiliriz.
        $min_days_for_calculation = max($days_period, 28) + 7;

        $start_date_for_loads = (clone $today)->modify('-' . ($min_days_for_calculation -1) . ' days');
        $end_date_for_loads = clone $today;

        $is_team_average = ($athlete_id === 'team_average');
        $all_daily_loads_source = [];

        if ($is_team_average) {
            $team_id = null;
            if (function_exists('coachai_get_current_coach_team_id')) {
                $team_id = coachai_get_current_coach_team_id($current_user_id, false);
            }
            if (!$team_id) {
                error_log("Load Analysis (Long Term): Team ID not found for team_average. User ID: " . $current_user_id);
                // Takım ID'si yoksa veya sporcu yoksa boş yük dizisi oluştur
                $current_loop_dt_init = clone $start_date_for_loads;
                while($current_loop_dt_init <= $end_date_for_loads){
                    $all_daily_loads_source[$current_loop_dt_init->format('Ymd')] = 0;
                    $current_loop_dt_init->modify('+1 day');
                }
            } else {
                $all_daily_loads_source = coachai_pd_get_daily_team_average_training_loads(
                    $team_id,
                    $start_date_for_loads->format('Ymd'),
                    $end_date_for_loads->format('Ymd'),
                    $current_user_id
                );
            }
        } else {
            $all_daily_loads_source = coachai_pd_get_daily_training_loads_for_athlete(
                intval($athlete_id),
                $start_date_for_loads->format('Ymd'),
                $end_date_for_loads->format('Ymd'),
                $current_user_id
            );
        }

        // Fill missing dates in all_daily_loads with 0 to ensure calculations are correct
        $all_daily_loads = [];
        $loop_dt_fill = clone $start_date_for_loads;
        while($loop_dt_fill <= $end_date_for_loads){
            $ymd_key_fill = $loop_dt_fill->format('Ymd');
            $all_daily_loads[$ymd_key_fill] = $all_daily_loads_source[$ymd_key_fill] ?? 0;
            $loop_dt_fill->modify('+1 day');
        }
        
        // Eğer hiç yük verisi yoksa (tüm günler 0 ise), boş array döndür.
        if (empty(array_filter($all_daily_loads))) {
             // Grafikte gösterilecek günler için etiketleri ve null/0 değerleri yine de oluşturabiliriz.
            $target_start_date_for_empty = (clone $today)->modify('-' . ($days_period - 1) . ' days');
            $current_loop_date_empty = clone $target_start_date_for_empty;
            while ($current_loop_date_empty <= $today) {
                $output['labels'][] = $current_loop_date_empty->format('M j');
                $output['daily_tl'][] = 0;
                $output['ra_acwr'][] = null;
                $output['ewma_acwr'][] = null;
                $current_loop_date_empty->modify('+1 day');
            }
            return $output;
        }

        $alpha_acute_ewma = 2 / (7 + 1); // 7 günlük akut pencere için yaklaşık
        $alpha_chronic_ewma = 2 / (28 + 1); // 28 günlük kronik pencere için yaklaşık
        $ewma_acwr_values = coachai_pd_calculate_ewma_acwr($all_daily_loads, $alpha_acute_ewma, $alpha_chronic_ewma);

        $ra_acute_window = 7;
        $ra_chronic_window = 28;
        $ra_acwr_values = coachai_pd_calculate_rolling_average_acwr($all_daily_loads, $ra_acute_window, $ra_chronic_window);

        $target_start_date = (clone $today)->modify('-' . ($days_period - 1) . ' days');
        $current_loop_date = clone $target_start_date;

        while ($current_loop_date <= $today) {
            $date_key_ymd = $current_loop_date->format('Ymd');
            $output['labels'][] = $current_loop_date->format('M j');
            $output['daily_tl'][] = isset($all_daily_loads[$date_key_ymd]) ? round($all_daily_loads[$date_key_ymd]) : 0;
            $output['ra_acwr'][] = isset($ra_acwr_values[$date_key_ymd]) ? $ra_acwr_values[$date_key_ymd] : null;
            $output['ewma_acwr'][] = isset($ewma_acwr_values[$date_key_ymd]) ? $ewma_acwr_values[$date_key_ymd] : null;
            $current_loop_date->modify('+1 day');
        }
        return $output;
    }
}

if ( ! function_exists( 'coachai_la_get_monotony_strain_chart_data' ) ) {
    function coachai_la_get_monotony_strain_chart_data( $athlete_id, $days_period, $current_user_id ) {
        $output = [
            'labels'   => [],
            'monotony' => [],
            'strain'   => [],
        ];
        $wp_timezone = wp_timezone();
        $today = new DateTime("now", $wp_timezone);
        // Monotony 7 günlük bir pencere kullanır. Strain de bu monotoniyi ve 7 günlük toplam yükü kullanır.
        // Grafikte $days_period kadar gün gösterilecekse, bu periyodun en başındaki gün için
        // 7 gün öncesine kadar veri gerekebilir.
        $start_date_for_loads = (clone $today)->modify('-' . (($days_period + 6)) . ' days');
        $end_date_for_loads = clone $today;

        $is_team_average = ($athlete_id === 'team_average');
        $all_daily_loads_source = [];

        if ($is_team_average) {
            $team_id = null;
            if (function_exists('coachai_get_current_coach_team_id')) {
                $team_id = coachai_get_current_coach_team_id($current_user_id, false);
            }
            if (!$team_id) {
                error_log("Load Analysis (Monotony/Strain): Team ID not found for team_average. User ID: " . $current_user_id);
                $current_loop_dt_init = clone $start_date_for_loads;
                while($current_loop_dt_init <= $end_date_for_loads){
                    $all_daily_loads_source[$current_loop_dt_init->format('Ymd')] = 0;
                    $current_loop_dt_init->modify('+1 day');
                }
            } else {
                $all_daily_loads_source = coachai_pd_get_daily_team_average_training_loads(
                    $team_id,
                    $start_date_for_loads->format('Ymd'),
                    $end_date_for_loads->format('Ymd'),
                    $current_user_id
                );
            }
        } else {
            $all_daily_loads_source = coachai_pd_get_daily_training_loads_for_athlete(
                intval($athlete_id),
                $start_date_for_loads->format('Ymd'),
                $end_date_for_loads->format('Ymd'),
                $current_user_id
            );
        }

        $all_daily_loads = [];
        $loop_dt_fill = clone $start_date_for_loads;
        while($loop_dt_fill <= $end_date_for_loads){
            $ymd_key_fill = $loop_dt_fill->format('Ymd');
            $all_daily_loads[$ymd_key_fill] = $all_daily_loads_source[$ymd_key_fill] ?? 0;
            $loop_dt_fill->modify('+1 day');
        }
        
        if (empty(array_filter($all_daily_loads))) {
            $target_start_date_for_empty = (clone $today)->modify('-' . ($days_period - 1) . ' days');
            $current_loop_date_empty = clone $target_start_date_for_empty;
            while ($current_loop_date_empty <= $today) {
                $output['labels'][] = $current_loop_date_empty->format('M j');
                $output['monotony'][] = null;
                $output['strain'][] = null;
                $current_loop_date_empty->modify('+1 day');
            }
            return $output;
        }

        $monotony_values = coachai_pd_calculate_monotony($all_daily_loads);
        $strain_values = coachai_pd_calculate_strain($all_daily_loads, $monotony_values);

        $target_start_date = (clone $today)->modify('-' . ($days_period - 1) . ' days');
        $current_loop_date = clone $target_start_date;

        while ($current_loop_date <= $today) {
            $date_key_ymd = $current_loop_date->format('Ymd');
            $output['labels'][] = $current_loop_date->format('M j');
            $output['monotony'][] = isset($monotony_values[$date_key_ymd]) ? $monotony_values[$date_key_ymd] : null;
            $output['strain'][] = isset($strain_values[$date_key_ymd]) ? round($strain_values[$date_key_ymd]) : null;
            $current_loop_date->modify('+1 day');
        }
        return $output;
    }
}

if ( ! function_exists( 'coachai_la_get_trend_analysis_card_data' ) ) {
    function coachai_la_get_trend_analysis_card_data( $athlete_id, $current_user_id, $overall_period_days = 60 ) {
        $output = [
            'avg_tl_last_7_days'         => 0,
            'avg_tl_overall'             => 0,
            'tl_percentage_change'       => 0,
            'current_ewma_acwr'          => null,
            'current_monotony'           => null,
            'is_load_increasing_warning' => false,
        ];
        $wp_timezone = wp_timezone();
        $today = new DateTime("now", $wp_timezone);

        // EWMA için en az 28 gün, Monotony için 7 gün, genel ortalama için $overall_period_days.
        // En uzun olanı ve biraz buffer alalım.
        $days_to_fetch = max($overall_period_days, 28 + 7); // ~35 gün veya $overall_period_days

        $start_date_for_loads = (clone $today)->modify('-' . ($days_to_fetch - 1) . ' days');
        $end_date_for_loads = clone $today;

        $is_team_average = ($athlete_id === 'team_average');
        $all_daily_loads_source = [];

        if ($is_team_average) {
            $team_id = null;
            if (function_exists('coachai_get_current_coach_team_id')) {
                $team_id = coachai_get_current_coach_team_id($current_user_id, false);
            }
             if (!$team_id) {
                error_log("Load Analysis (Trend): Team ID not found for team_average. User ID: " . $current_user_id);
                // Tüm yükleri 0 olarak ayarla, böylece hesaplamalar 0 veya null döndürür.
                $current_loop_dt_init = clone $start_date_for_loads;
                while($current_loop_dt_init <= $end_date_for_loads){
                    $all_daily_loads_source[$current_loop_dt_init->format('Ymd')] = 0;
                    $current_loop_dt_init->modify('+1 day');
                }
            } else {
                $all_daily_loads_source = coachai_pd_get_daily_team_average_training_loads(
                    $team_id,
                    $start_date_for_loads->format('Ymd'),
                    $end_date_for_loads->format('Ymd'),
                    $current_user_id
                );
            }
        } else {
            $all_daily_loads_source = coachai_pd_get_daily_training_loads_for_athlete(
                intval($athlete_id),
                $start_date_for_loads->format('Ymd'),
                $end_date_for_loads->format('Ymd'),
                $current_user_id
            );
        }
        
        $all_daily_loads = [];
        $loop_dt_fill = clone $start_date_for_loads;
        while($loop_dt_fill <= $end_date_for_loads){
            $ymd_key_fill = $loop_dt_fill->format('Ymd');
            $all_daily_loads[$ymd_key_fill] = $all_daily_loads_source[$ymd_key_fill] ?? 0;
            $loop_dt_fill->modify('+1 day');
        }

        // Eğer yükler tamamen boşsa (tüm değerler 0 ise), fonksiyon varsayılan $output değerleriyle dönecektir.
        // Bu genellikle istenen davranıştır (0'lar ve null'lar gösterilir).
        if (empty(array_filter($all_daily_loads))) {
            // current_ewma_acwr ve current_monotony null kalacak, diğerleri 0 olacak. Bu doğru.
            return $output;
        }
        
        $sum_last_7_days = 0;
        $count_active_last_7 = 0; // Sadece yük olan günlerin ortalamasını almak için
        for ($i = 0; $i < 7; $i++) {
            $d_key = (clone $today)->modify("-$i days")->format('Ymd');
            if (isset($all_daily_loads[$d_key])) { // Bu anahtarın var olduğundan emin ol
                $load_val = $all_daily_loads[$d_key];
                $sum_last_7_days += $load_val;
                if ($load_val > 0) { // Sadece aktif günleri say
                    $count_active_last_7++;
                }
            }
        }
        // Eğer son 7 günde hiç aktif gün yoksa ortalama 0 olmalı.
        // Eğer aktif gün varsa, ortalama toplam yük / aktif gün sayısı olmalı.
        // Eğer hem toplam yük 0 hem de aktif gün 0 ise, ortalama yine 0 olur.
        $output['avg_tl_last_7_days'] = ($count_active_last_7 > 0) ? round($sum_last_7_days / $count_active_last_7) : 0;


        $sum_overall = 0;
        $count_active_overall = 0; // Sadece yük olan günlerin ortalamasını almak için
        for ($i = 0; $i < $overall_period_days; $i++) {
            $d_key = (clone $today)->modify("-$i days")->format('Ymd');
            if (isset($all_daily_loads[$d_key])) {
                $load_val_overall = $all_daily_loads[$d_key];
                $sum_overall += $load_val_overall;
                if ($load_val_overall > 0) {
                    $count_active_overall++;
                }
            }
        }
        $output['avg_tl_overall'] = ($count_active_overall > 0) ? round($sum_overall / $count_active_overall) : 0;

        if ($output['avg_tl_overall'] > 0) {
            $output['tl_percentage_change'] = round((($output['avg_tl_last_7_days'] - $output['avg_tl_overall']) / $output['avg_tl_overall']) * 100, 1);
        } elseif ($output['avg_tl_last_7_days'] > 0) { // Overall 0 iken son 7 gün yükü varsa %100 artış gibi kabul edilebilir
            $output['tl_percentage_change'] = 100.0;
        } else {
            $output['tl_percentage_change'] = 0; // Her ikisi de 0 ise değişim 0
        }

        $alpha_acute_ewma = 2 / (7 + 1);
        $alpha_chronic_ewma = 2 / (28 + 1);
        $ewma_acwr_values = coachai_pd_calculate_ewma_acwr($all_daily_loads, $alpha_acute_ewma, $alpha_chronic_ewma);
        $today_ymd = $today->format('Ymd');
        $output['current_ewma_acwr'] = isset($ewma_acwr_values[$today_ymd]) ? $ewma_acwr_values[$today_ymd] : null;

        $monotony_values = coachai_pd_calculate_monotony($all_daily_loads);
        $output['current_monotony'] = isset($monotony_values[$today_ymd]) ? $monotony_values[$today_ymd] : null;

        // Uyarı mantığı: Monotoni 2'den büyükse VE EWMA ACWR 1.3'ten büyükse VE TL yüzdelik değişimi %15'ten fazlaysa
        if ( ($output['current_monotony'] !== null && $output['current_monotony'] > 2.0) &&
             ($output['current_ewma_acwr'] !== null && $output['current_ewma_acwr'] > 1.3) &&
             ($output['tl_percentage_change'] > 15.0) ) {
            $output['is_load_increasing_warning'] = true;
        }

        return $output;
    }
}

if ( ! function_exists( 'coachai_la_get_weekly_summary_chart_data' ) ) {
    function coachai_la_get_weekly_summary_chart_data( $athlete_id, $weeks_count, $current_user_id, $current_lang = 'en' ) {
        $output = [
            'week_labels'                   => [],
            'weekly_total_loads'            => [],
            'weekly_tl_percentage_changes'  => [],
        ];
        $wp_timezone = wp_timezone();
        $today = new DateTime("now", $wp_timezone);
        $start_of_week_option = (int) get_option( 'start_of_week', 1 ); // Pazartesi=1, Pazar=0 (PHP: Pazar=0, Pzt=1..Cmt=6)

        // Haftanın başlangıç gününü WP ayarına göre ayarla
        $php_w_today = $today->format('w'); // PHP: 0 (Sun) to 6 (Sat)
        // WP: 0 (Sun), 1 (Mon), ..., 6 (Sat)
        // Eğer WP ayarı 0 (Pazar) ve PHP'de bugün Pazar (0) ise, fark 0.
        // Eğer WP ayarı 1 (Pazartesi) ve PHP'de bugün Pazar (0) ise, -1 -> 6 gün geri gitmeli (modify('-0 days') - modify('-1 days'))
        // Eğer WP ayarı 1 (Pazartesi) ve PHP'de bugün Pazartesi (1) ise, fark 0.
        // Eğer WP ayarı 1 (Pazartesi) ve PHP'de bugün Salı (2) ise, fark 1 -> 1 gün geri gitmeli.
        $days_to_subtract_for_current_week_monday = ($php_w_today - $start_of_week_option + 7) % 7;
        $current_week_start_day = (clone $today)->modify("-{$days_to_subtract_for_current_week_monday} days");


        // Haftalık yükler için $weeks_count kadar hafta öncesine gitmeliyiz.
        // Herhangi bir hesaplama için ek geçmişe ihtiyaç yok, sadece o haftaların yükleri.
        $start_date_for_loads = (clone $current_week_start_day)->modify('-' . ($weeks_count -1) . ' weeks');
        // Bitiş tarihi, mevcut haftanın sonu olmalı, ancak bugünü geçmemeli.
        $end_date_for_loads = (clone $current_week_start_day)->modify('+6 days');
        if ($end_date_for_loads > $today) {
            $end_date_for_loads = clone $today;
        }

        $is_team_average = ($athlete_id === 'team_average');
        $all_daily_loads_source = [];

        if ($is_team_average) {
            $team_id = null;
            if (function_exists('coachai_get_current_coach_team_id')) {
                $team_id = coachai_get_current_coach_team_id($current_user_id, false);
            }
            if (!$team_id) {
                error_log("Load Analysis (Weekly Summary): Team ID not found for team_average. User ID: " . $current_user_id);
                 $current_loop_dt_init = clone $start_date_for_loads;
                while($current_loop_dt_init <= $end_date_for_loads){
                    $all_daily_loads_source[$current_loop_dt_init->format('Ymd')] = 0;
                    $current_loop_dt_init->modify('+1 day');
                }
            } else {
                $all_daily_loads_source = coachai_pd_get_daily_team_average_training_loads(
                    $team_id,
                    $start_date_for_loads->format('Ymd'),
                    $end_date_for_loads->format('Ymd'),
                    $current_user_id
                );
            }
        } else {
            $all_daily_loads_source = coachai_pd_get_daily_training_loads_for_athlete(
                intval($athlete_id),
                $start_date_for_loads->format('Ymd'),
                $end_date_for_loads->format('Ymd'),
                $current_user_id
            );
        }
        
        $all_daily_loads = [];
        $loop_dt_fill = clone $start_date_for_loads;
        while($loop_dt_fill <= $end_date_for_loads){ // $end_date_for_loads'a kadar doldur
            $ymd_key_fill = $loop_dt_fill->format('Ymd');
            $all_daily_loads[$ymd_key_fill] = $all_daily_loads_source[$ymd_key_fill] ?? 0;
            $loop_dt_fill->modify('+1 day');
        }

        if (empty(array_filter($all_daily_loads))) {
            for ($w = ($weeks_count - 1); $w >= 0; $w--) {
                $week_start_date_for_empty = (clone $current_week_start_day)->modify('-' . $w . ' weeks');
                $output['week_labels'][] = $week_start_date_for_empty->format('M j') . ($current_lang === 'tr' ? ' Hft' : ' Wk');
                $output['weekly_total_loads'][] = 0;
                $output['weekly_tl_percentage_changes'][] = 0;
            }
            return $output;
        }

        $weekly_loads_temp = [];
        for ($w = ($weeks_count - 1); $w >= 0; $w--) { // En eski haftadan en yeniye doğru
            $week_start_date_loop = (clone $current_week_start_day)->modify('-' . $w . ' weeks');
            $week_end_date_for_sum = (clone $week_start_date_loop)->modify('+6 days');

            $output['week_labels'][] = $week_start_date_loop->format('M j') . ($current_lang === 'tr' ? ' Hft' : ' Wk');

            $current_week_sum = 0;
            $loop_date_sum = clone $week_start_date_loop;
            while ($loop_date_sum <= $week_end_date_for_sum) {
                $date_key_ymd = $loop_date_sum->format('Ymd');
                if (isset($all_daily_loads[$date_key_ymd])) {
                    $current_week_sum += $all_daily_loads[$date_key_ymd];
                }
                $loop_date_sum->modify('+1 day');
            }
            $weekly_loads_temp[] = round($current_week_sum);
        }
        $output['weekly_total_loads'] = $weekly_loads_temp;

        $previous_week_load = null;
        foreach ($output['weekly_total_loads'] as $load_iter) {
            if ($previous_week_load !== null) {
                if ($previous_week_load > 0) {
                    $output['weekly_tl_percentage_changes'][] = round((($load_iter - $previous_week_load) / $previous_week_load) * 100, 1);
                } elseif ($load_iter > 0) { // Önceki 0, şimdiki > 0 ise %100 artış
                    $output['weekly_tl_percentage_changes'][] = 100.0;
                } else { // Her ikisi de 0 ise değişim 0
                    $output['weekly_tl_percentage_changes'][] = 0;
                }
            } else {
                $output['weekly_tl_percentage_changes'][] = 0; // İlk hafta için değişim 0
            }
            $previous_week_load = $load_iter;
        }
        return $output;
    }
}


if ( ! function_exists( 'coachai_la_get_combined_weekly_metrics_chart_data' ) ) {
    function coachai_la_get_combined_weekly_metrics_chart_data( $athlete_id, $weeks_count, $current_user_id, $current_lang = 'en' ) {
        $output = [
            'week_labels'       => [],
            'weekly_ewma_acwr'  => [],
            'weekly_monotony'   => [],
            'weekly_strain'     => [],
        ];
        $wp_timezone = wp_timezone();
        $today = new DateTime("now", $wp_timezone);
        $start_of_week_option = (int) get_option( 'start_of_week', 1 );

        $php_w_today = $today->format('w');
        $days_to_subtract_for_current_week_monday = ($php_w_today - $start_of_week_option + 7) % 7;
        $current_week_start_day = (clone $today)->modify("-{$days_to_subtract_for_current_week_monday} days");

        // EWMA/Monotony/Strain için ek geçmiş veri gerekli.
        // Monotony için 7 gün, EWMA için 28 gün. En az 28 gün + $weeks_count kadar geçmiş lazım.
        // Yaklaşık 4-5 hafta (28-35 gün) ek geçmiş yeterli olmalı.
        $history_needed_weeks = $weeks_count + 5;
        $start_date_for_loads = (clone $current_week_start_day)->modify('-' . ($history_needed_weeks -1) . ' weeks');
        $end_date_for_loads = (clone $current_week_start_day)->modify('+6 days');
        if ($end_date_for_loads > $today) {
            $end_date_for_loads = clone $today;
        }

        $is_team_average = ($athlete_id === 'team_average');
        $all_daily_loads_source = [];

        if ($is_team_average) {
            $team_id = null;
            if (function_exists('coachai_get_current_coach_team_id')) {
                $team_id = coachai_get_current_coach_team_id($current_user_id, false);
            }
            if (!$team_id) {
                error_log("Load Analysis (Weekly Metrics): Team ID not found for team_average. User ID: " . $current_user_id);
                $current_loop_dt_init = clone $start_date_for_loads;
                while($current_loop_dt_init <= $end_date_for_loads){
                    $all_daily_loads_source[$current_loop_dt_init->format('Ymd')] = 0;
                    $current_loop_dt_init->modify('+1 day');
                }
            } else {
                $all_daily_loads_source = coachai_pd_get_daily_team_average_training_loads(
                    $team_id,
                    $start_date_for_loads->format('Ymd'),
                    $end_date_for_loads->format('Ymd'),
                    $current_user_id
                );
            }
        } else {
            $all_daily_loads_source = coachai_pd_get_daily_training_loads_for_athlete(
                intval($athlete_id),
                $start_date_for_loads->format('Ymd'),
                $end_date_for_loads->format('Ymd'),
                $current_user_id
            );
        }

        $all_daily_loads = [];
        $loop_dt_fill = clone $start_date_for_loads;
        while($loop_dt_fill <= $end_date_for_loads){
            $ymd_key_fill = $loop_dt_fill->format('Ymd');
            $all_daily_loads[$ymd_key_fill] = $all_daily_loads_source[$ymd_key_fill] ?? 0;
            $loop_dt_fill->modify('+1 day');
        }

        if (empty(array_filter($all_daily_loads))) {
            for ($w = ($weeks_count - 1); $w >= 0; $w--) {
                $week_start_date_for_empty = (clone $current_week_start_day)->modify('-' . $w . ' weeks');
                $output['week_labels'][] = $week_start_date_for_empty->format('M j') . ($current_lang === 'tr' ? ' Hft' : ' Wk');
                $output['weekly_ewma_acwr'][] = null;
                $output['weekly_monotony'][] = null;
                $output['weekly_strain'][] = null;
            }
            return $output;
        }

        $alpha_acute_ewma = 2 / (7 + 1);
        $alpha_chronic_ewma = 2 / (28 + 1);
        $daily_ewma_acwr = coachai_pd_calculate_ewma_acwr($all_daily_loads, $alpha_acute_ewma, $alpha_chronic_ewma);
        $daily_monotony = coachai_pd_calculate_monotony($all_daily_loads);
        $daily_strain = coachai_pd_calculate_strain($all_daily_loads, $daily_monotony);

        for ($w = ($weeks_count - 1); $w >= 0; $w--) { // En eski haftadan en yeniye
            $week_start_date_loop = (clone $current_week_start_day)->modify('-' . $w . ' weeks');
            // Haftanın son günü, metriklerin hesaplanacağı gün
            $last_day_of_this_week_obj = (clone $week_start_date_loop)->modify('+6 days');
            // Eğer bu tarih bugünden ilerideyse, bugünü kullan
            if ($last_day_of_this_week_obj > $today) {
                $last_day_of_this_week_obj = clone $today;
            }
            $last_day_key_ymd = $last_day_of_this_week_obj->format('Ymd');

            $output['week_labels'][] = $week_start_date_loop->format('M j') . ($current_lang === 'tr' ? ' Hft' : ' Wk');

            $output['weekly_ewma_acwr'][] = isset($daily_ewma_acwr[$last_day_key_ymd]) ? $daily_ewma_acwr[$last_day_key_ymd] : null;
            $output['weekly_monotony'][] = isset($daily_monotony[$last_day_key_ymd]) ? $daily_monotony[$last_day_key_ymd] : null;
            $output['weekly_strain'][] = isset($daily_strain[$last_day_key_ymd]) ? round($daily_strain[$last_day_key_ymd]) : null;
        }
        return $output;
    }
}
?>